<?php

namespace App\Filament\Resources\JadwalAbsensiMasalResource\Pages;

use App\Filament\Resources\JadwalAbsensiMasalResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditJadwalAbsensiMasal extends EditRecord
{
    protected static string $resource = JadwalAbsensiMasalResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->visible(fn() => !$this->getRecord()->isGenerated()),
            Actions\Action::make('back')
                ->label('Kembali')
                ->url($this->getResource()::getUrl('index'))
                ->icon('heroicon-o-arrow-left')
                ->color('gray'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Jadwal & Absensi Masal berhasil diperbarui';
    }
}
