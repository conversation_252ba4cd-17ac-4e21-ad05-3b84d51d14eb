<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.inventory.resources.goods-receipt-resource.pages.create-goods-receipt' => 'App\\Filament\\Inventory\\Resources\\GoodsReceiptResource\\Pages\\CreateGoodsReceipt',
    'app.filament.inventory.resources.goods-receipt-resource.pages.edit-goods-receipt' => 'App\\Filament\\Inventory\\Resources\\GoodsReceiptResource\\Pages\\EditGoodsReceipt',
    'app.filament.inventory.resources.goods-receipt-resource.pages.list-goods-receipts' => 'App\\Filament\\Inventory\\Resources\\GoodsReceiptResource\\Pages\\ListGoodsReceipts',
    'app.filament.inventory.resources.goods-receipt-resource.pages.view-goods-receipt' => 'App\\Filament\\Inventory\\Resources\\GoodsReceiptResource\\Pages\\ViewGoodsReceipt',
    'app.filament.inventory.resources.goods-receipt-resource.relation-managers.goods-receipt-items-relation-manager' => 'App\\Filament\\Inventory\\Resources\\GoodsReceiptResource\\RelationManagers\\GoodsReceiptItemsRelationManager',
    'app.filament.inventory.resources.product-resource.pages.create-product' => 'App\\Filament\\Inventory\\Resources\\ProductResource\\Pages\\CreateProduct',
    'app.filament.inventory.resources.product-resource.pages.edit-product' => 'App\\Filament\\Inventory\\Resources\\ProductResource\\Pages\\EditProduct',
    'app.filament.inventory.resources.product-resource.pages.list-products' => 'App\\Filament\\Inventory\\Resources\\ProductResource\\Pages\\ListProducts',
    'app.filament.inventory.resources.product-resource.pages.view-product' => 'App\\Filament\\Inventory\\Resources\\ProductResource\\Pages\\ViewProduct',
    'app.filament.inventory.resources.purchase-order-resource.pages.create-purchase-order' => 'App\\Filament\\Inventory\\Resources\\PurchaseOrderResource\\Pages\\CreatePurchaseOrder',
    'app.filament.inventory.resources.purchase-order-resource.pages.edit-purchase-order' => 'App\\Filament\\Inventory\\Resources\\PurchaseOrderResource\\Pages\\EditPurchaseOrder',
    'app.filament.inventory.resources.purchase-order-resource.pages.list-purchase-orders' => 'App\\Filament\\Inventory\\Resources\\PurchaseOrderResource\\Pages\\ListPurchaseOrders',
    'app.filament.inventory.resources.purchase-order-resource.pages.view-purchase-order' => 'App\\Filament\\Inventory\\Resources\\PurchaseOrderResource\\Pages\\ViewPurchaseOrder',
    'app.filament.inventory.resources.sales-order-resource.pages.create-sales-order' => 'App\\Filament\\Inventory\\Resources\\SalesOrderResource\\Pages\\CreateSalesOrder',
    'app.filament.inventory.resources.sales-order-resource.pages.edit-sales-order' => 'App\\Filament\\Inventory\\Resources\\SalesOrderResource\\Pages\\EditSalesOrder',
    'app.filament.inventory.resources.sales-order-resource.pages.list-sales-orders' => 'App\\Filament\\Inventory\\Resources\\SalesOrderResource\\Pages\\ListSalesOrders',
    'app.filament.inventory.resources.sales-order-resource.pages.view-sales-order' => 'App\\Filament\\Inventory\\Resources\\SalesOrderResource\\Pages\\ViewSalesOrder',
    'app.filament.inventory.resources.stock-adjustment-resource.pages.create-stock-adjustment' => 'App\\Filament\\Inventory\\Resources\\StockAdjustmentResource\\Pages\\CreateStockAdjustment',
    'app.filament.inventory.resources.stock-adjustment-resource.pages.edit-stock-adjustment' => 'App\\Filament\\Inventory\\Resources\\StockAdjustmentResource\\Pages\\EditStockAdjustment',
    'app.filament.inventory.resources.stock-adjustment-resource.pages.list-stock-adjustments' => 'App\\Filament\\Inventory\\Resources\\StockAdjustmentResource\\Pages\\ListStockAdjustments',
    'app.filament.inventory.resources.stock-adjustment-resource.pages.view-stock-adjustment' => 'App\\Filament\\Inventory\\Resources\\StockAdjustmentResource\\Pages\\ViewStockAdjustment',
    'app.filament.inventory.resources.stock-movement-resource.pages.create-stock-movement' => 'App\\Filament\\Inventory\\Resources\\StockMovementResource\\Pages\\CreateStockMovement',
    'app.filament.inventory.resources.stock-movement-resource.pages.edit-stock-movement' => 'App\\Filament\\Inventory\\Resources\\StockMovementResource\\Pages\\EditStockMovement',
    'app.filament.inventory.resources.stock-movement-resource.pages.list-stock-movements' => 'App\\Filament\\Inventory\\Resources\\StockMovementResource\\Pages\\ListStockMovements',
    'app.filament.inventory.resources.stock-movement-resource.pages.view-stock-movement' => 'App\\Filament\\Inventory\\Resources\\StockMovementResource\\Pages\\ViewStockMovement',
    'app.filament.inventory.resources.stock-opname-resource.pages.create-stock-opname' => 'App\\Filament\\Inventory\\Resources\\StockOpnameResource\\Pages\\CreateStockOpname',
    'app.filament.inventory.resources.stock-opname-resource.pages.edit-stock-opname' => 'App\\Filament\\Inventory\\Resources\\StockOpnameResource\\Pages\\EditStockOpname',
    'app.filament.inventory.resources.stock-opname-resource.pages.list-stock-opnames' => 'App\\Filament\\Inventory\\Resources\\StockOpnameResource\\Pages\\ListStockOpnames',
    'app.filament.inventory.resources.stock-opname-resource.relation-managers.stock-opname-items-relation-manager' => 'App\\Filament\\Inventory\\Resources\\StockOpnameResource\\RelationManagers\\StockOpnameItemsRelationManager',
    'app.filament.inventory.resources.supplier-resource.pages.create-supplier' => 'App\\Filament\\Inventory\\Resources\\SupplierResource\\Pages\\CreateSupplier',
    'app.filament.inventory.resources.supplier-resource.pages.edit-supplier' => 'App\\Filament\\Inventory\\Resources\\SupplierResource\\Pages\\EditSupplier',
    'app.filament.inventory.resources.supplier-resource.pages.list-suppliers' => 'App\\Filament\\Inventory\\Resources\\SupplierResource\\Pages\\ListSuppliers',
    'app.filament.inventory.resources.supplier-resource.pages.view-supplier' => 'App\\Filament\\Inventory\\Resources\\SupplierResource\\Pages\\ViewSupplier',
    'app.filament.inventory.resources.supplier-resource.relation-managers.purchase-orders-relation-manager' => 'App\\Filament\\Inventory\\Resources\\SupplierResource\\RelationManagers\\PurchaseOrdersRelationManager',
    'app.filament.inventory.resources.warehouse-resource.pages.create-warehouse' => 'App\\Filament\\Inventory\\Resources\\WarehouseResource\\Pages\\CreateWarehouse',
    'app.filament.inventory.resources.warehouse-resource.pages.edit-warehouse' => 'App\\Filament\\Inventory\\Resources\\WarehouseResource\\Pages\\EditWarehouse',
    'app.filament.inventory.resources.warehouse-resource.pages.list-warehouses' => 'App\\Filament\\Inventory\\Resources\\WarehouseResource\\Pages\\ListWarehouses',
    'app.filament.inventory.resources.warehouse-resource.pages.view-warehouse' => 'App\\Filament\\Inventory\\Resources\\WarehouseResource\\Pages\\ViewWarehouse',
    'app.filament.inventory.pages.dashboard' => 'App\\Filament\\Inventory\\Pages\\Dashboard',
    'app.filament.inventory.pages.movement-history-report' => 'App\\Filament\\Inventory\\Pages\\MovementHistoryReport',
    'app.filament.inventory.pages.stock-overview' => 'App\\Filament\\Inventory\\Pages\\StockOverview',
    'app.filament.inventory.pages.stock-report' => 'App\\Filament\\Inventory\\Pages\\StockReport',
    'app.filament.inventory.widgets.active-warehouses-widget' => 'App\\Filament\\Inventory\\Widgets\\ActiveWarehousesWidget',
    'app.filament.inventory.widgets.low-stock-alerts-widget' => 'App\\Filament\\Inventory\\Widgets\\LowStockAlertsWidget',
    'app.filament.inventory.widgets.pending-orders-widget' => 'App\\Filament\\Inventory\\Widgets\\PendingOrdersWidget',
    'app.filament.inventory.widgets.stock-movement-chart' => 'App\\Filament\\Inventory\\Widgets\\StockMovementChart',
    'app.filament.inventory.widgets.total-products-widget' => 'App\\Filament\\Inventory\\Widgets\\TotalProductsWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Pages\\Dashboard.php' => 'App\\Filament\\Inventory\\Pages\\Dashboard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Pages\\MovementHistoryReport.php' => 'App\\Filament\\Inventory\\Pages\\MovementHistoryReport',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Pages\\StockOverview.php' => 'App\\Filament\\Inventory\\Pages\\StockOverview',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Pages\\StockReport.php' => 'App\\Filament\\Inventory\\Pages\\StockReport',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Inventory/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Inventory\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Resources\\GoodsReceiptResource.php' => 'App\\Filament\\Inventory\\Resources\\GoodsReceiptResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Resources\\ProductResource.php' => 'App\\Filament\\Inventory\\Resources\\ProductResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Resources\\PurchaseOrderResource.php' => 'App\\Filament\\Inventory\\Resources\\PurchaseOrderResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Resources\\SalesOrderResource.php' => 'App\\Filament\\Inventory\\Resources\\SalesOrderResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Resources\\StockAdjustmentResource.php' => 'App\\Filament\\Inventory\\Resources\\StockAdjustmentResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Resources\\StockMovementResource.php' => 'App\\Filament\\Inventory\\Resources\\StockMovementResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Resources\\StockOpnameResource.php' => 'App\\Filament\\Inventory\\Resources\\StockOpnameResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Resources\\SupplierResource.php' => 'App\\Filament\\Inventory\\Resources\\SupplierResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Resources\\WarehouseResource.php' => 'App\\Filament\\Inventory\\Resources\\WarehouseResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Inventory/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Inventory\\Resources',
  ),
  'widgets' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Widgets\\ActiveWarehousesWidget.php' => 'App\\Filament\\Inventory\\Widgets\\ActiveWarehousesWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Widgets\\LowStockAlertsWidget.php' => 'App\\Filament\\Inventory\\Widgets\\LowStockAlertsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Widgets\\PendingOrdersWidget.php' => 'App\\Filament\\Inventory\\Widgets\\PendingOrdersWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Widgets\\StockMovementChart.php' => 'App\\Filament\\Inventory\\Widgets\\StockMovementChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Inventory\\Widgets\\TotalProductsWidget.php' => 'App\\Filament\\Inventory\\Widgets\\TotalProductsWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Inventory/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Inventory\\Widgets',
  ),
);