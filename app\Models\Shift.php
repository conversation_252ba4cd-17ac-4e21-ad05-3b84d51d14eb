<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Shift extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'shift';

    protected $fillable = [
        'nama_shift',
        'waktu_mulai',
        'waktu_selesai',
        'toleransi_keterlambatan',
        'is_split_shift',
        'is_beda_hari',
        'is_beda_hari2',
        'waktu_mulai_periode2',
        'waktu_selesai_periode2',
        'toleransi_keterlambatan_periode2',
        'keterangan',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_split_shift' => 'boolean',
        'is_beda_hari' => 'boolean',
        'is_beda_hari2' => 'boolean',
        'waktu_mulai' => 'datetime:H:i:s',
        'waktu_selesai' => 'datetime:H:i:s',
        'waktu_mulai_periode2' => 'datetime:H:i:s',
        'waktu_selesai_periode2' => 'datetime:H:i:s',
    ];

    /**
     * Get the schedules associated with this shift
     */
    public function schedules()
    {
        return $this->hasMany(Schedule::class, 'shift_id');
    }

    /**
     * Get the user who created this shift
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the bulk schedules associated with this shift
     */
    public function jadwalMasal()
    {
        return $this->hasMany(JadwalMasal::class, 'shift_id');
    }

    /**
     * Check if this is a split shift
     */
    public function isSplitShift(): bool
    {
        return $this->is_split_shift &&
            $this->waktu_mulai_periode2 !== null &&
            $this->waktu_selesai_periode2 !== null;
    }

    /**
     * Check if this shift spans across different days (overnight shift)
     */
    public function isBedaHari(): bool
    {
        return $this->is_beda_hari;
    }

    /**
     * Check if split shift period 2 spans across different days
     */
    public function isBedaHari2(): bool
    {
        return $this->is_beda_hari2;
    }

    /**
     * Automatically detect if shift should be marked as beda hari
     * based on waktu_selesai being earlier than waktu_mulai
     */
    public function shouldBeBedaHari(): bool
    {
        if (!$this->waktu_mulai || !$this->waktu_selesai) {
            return false;
        }

        $start = \Carbon\Carbon::parse($this->waktu_mulai);
        $end = \Carbon\Carbon::parse($this->waktu_selesai);

        // If end time is earlier than start time, it's an overnight shift
        return $end->lessThan($start);
    }

    /**
     * Automatically detect if split shift period 2 should be marked as beda hari
     */
    public function shouldBeBedaHari2(): bool
    {
        if (!$this->isSplitShift() || !$this->waktu_mulai_periode2 || !$this->waktu_selesai_periode2) {
            return false;
        }

        $start = \Carbon\Carbon::parse($this->waktu_mulai_periode2);
        $end = \Carbon\Carbon::parse($this->waktu_selesai_periode2);

        // If end time is earlier than start time, it's an overnight shift
        return $end->lessThan($start);
    }

    /**
     * Auto-set is_beda_hari based on time comparison
     */
    public function autoSetBedaHari(): void
    {
        $this->is_beda_hari = $this->shouldBeBedaHari();
    }

    /**
     * Auto-set is_beda_hari2 based on time comparison for split shift period 2
     */
    public function autoSetBedaHari2(): void
    {
        $this->is_beda_hari2 = $this->shouldBeBedaHari2();
    }

    /**
     * Auto-set both beda hari flags based on time comparison
     */
    public function autoSetAllBedaHari(): void
    {
        $this->autoSetBedaHari();
        $this->autoSetBedaHari2();
    }

    /**
     * Get all work periods for this shift
     * Returns array of periods with start and end times
     */
    public function getWorkPeriods(): array
    {
        $periods = [
            [
                'periode' => 1,
                'waktu_mulai' => $this->waktu_mulai,
                'waktu_selesai' => $this->waktu_selesai,
                'toleransi_keterlambatan' => $this->toleransi_keterlambatan,
            ]
        ];

        if ($this->isSplitShift()) {
            $periods[] = [
                'periode' => 2,
                'waktu_mulai' => $this->waktu_mulai_periode2,
                'waktu_selesai' => $this->waktu_selesai_periode2,
                'toleransi_keterlambatan' => $this->toleransi_keterlambatan_periode2 ?? $this->toleransi_keterlambatan,
            ];
        }

        return $periods;
    }

    /**
     * Determine which period the given time falls into
     */
    public function getCurrentPeriod($time = null): ?int
    {
        if (!$time) {
            $time = now()->format('H:i:s');
        }

        $timeCarbon = \Carbon\Carbon::parse($time);
        $periods = $this->getWorkPeriods();

        foreach ($periods as $period) {
            $start = \Carbon\Carbon::parse($period['waktu_mulai']);
            $end = \Carbon\Carbon::parse($period['waktu_selesai']);

            // Handle overnight shifts
            if ($end->lessThan($start)) {
                $end->addDay();
                if ($timeCarbon->lessThan($start)) {
                    $timeCarbon->addDay();
                }
            }

            // Check if time is within this period (with some buffer)
            $bufferStart = $start->copy()->subHours(2); // 2 hours before start
            $bufferEnd = $end->copy()->addHours(2); // 2 hours after end

            if ($timeCarbon->between($bufferStart, $bufferEnd)) {
                return $period['periode'];
            }
        }

        return 1; // Default to first period
    }

    /**
     * Get the correct checkout date for this shift
     * Returns the next day if it's a beda hari shift, otherwise same day
     */
    public function getCheckoutDate(\Carbon\Carbon $checkinDate, int $periode = 1): \Carbon\Carbon
    {
        if ($periode == 1 && $this->isBedaHari()) {
            return $checkinDate->copy()->addDay();
        }

        if ($periode == 2 && $this->isBedaHari2()) {
            return $checkinDate->copy()->addDay();
        }

        return $checkinDate->copy();
    }

    /**
     * Check if checkout is allowed on the given date for this shift
     */
    public function isCheckoutAllowedOnDate(\Carbon\Carbon $checkinDate, \Carbon\Carbon $checkoutDate, int $periode = 1): bool
    {
        if ($periode == 1 && $this->isBedaHari()) {
            // For overnight shifts period 1, checkout should be on the next day
            return $checkoutDate->isSameDay($checkinDate->copy()->addDay());
        }

        if ($periode == 2 && $this->isBedaHari2()) {
            // For overnight shifts period 2, checkout should be on the next day
            return $checkoutDate->isSameDay($checkinDate->copy()->addDay());
        }

        // For regular shifts, checkout should be on the same day
        return $checkoutDate->isSameDay($checkinDate);
    }

    /**
     * Get formatted display text for the shift
     */
    public function getDisplayText(): string
    {
        if ($this->isSplitShift()) {
            $bedaHariText1 = $this->isBedaHari() ? ' (+1)' : '';
            $bedaHariText2 = $this->isBedaHari2() ? ' (+1)' : '';

            return sprintf(
                '%s (Split: %s-%s%s & %s-%s%s)',
                $this->nama_shift,
                substr($this->waktu_mulai, 0, 5),
                substr($this->waktu_selesai, 0, 5),
                $bedaHariText1,
                substr($this->waktu_mulai_periode2, 0, 5),
                substr($this->waktu_selesai_periode2, 0, 5),
                $bedaHariText2
            );
        }

        $bedaHariText = $this->isBedaHari() ? ' (+1)' : '';
        return sprintf(
            '%s (%s-%s%s)',
            $this->nama_shift,
            substr($this->waktu_mulai, 0, 5),
            substr($this->waktu_selesai, 0, 5),
            $bedaHariText
        );
    }
}
