<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\JadwalAbsensiMasal;
use App\Models\Shift;
use App\Models\Entitas;
use App\Models\Karyawan;
use App\Models\User;
use Carbon\Carbon;

class JadwalAbsensiMasalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get sample data
        $shift = Shift::where('is_active', true)->first();
        $entitas = Entitas::where('is_active', true)->first();
        $user = User::where('role', 'admin')->first() ?? User::first();
        $karyawan = Karyawan::where('status_aktif', true)->limit(5)->get();

        if (!$shift || !$entitas || !$user || $karyawan->isEmpty()) {
            $this->command->warn('Tidak dapat membuat sample data. Pastikan ada data Shift, Enti<PERSON>, User, dan <PERSON>.');
            return;
        }

        // Create sample jadwal absensi masal
        $jadwalAbsensiMasal = JadwalAbsensiMasal::create([
            'nama_jadwal' => 'Jadwal Januari 2025 - Shift Pagi',
            'tanggal_mulai' => Carbon::now()->startOfMonth(),
            'tanggal_selesai' => Carbon::now()->endOfMonth(),
            'shift_id' => $shift->id,
            'entitas_id' => $entitas->id,
            'created_by' => $user->id,
            'keterangan' => 'Jadwal dan absensi masal untuk bulan Januari 2025',
            'status' => 'draft',
        ]);

        // Attach karyawan
        $jadwalAbsensiMasal->karyawan()->attach($karyawan->pluck('id'));

        $this->command->info('Sample Jadwal Absensi Masal berhasil dibuat dengan ' . $karyawan->count() . ' karyawan.');
    }
}
