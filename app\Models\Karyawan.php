<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Karyawan extends Model
{
    use HasFactory, SoftDeletes, \App\Traits\HasOptimizedQueries;

    protected $table = 'karyawan';

    protected $fillable = [
        'nama_lengkap',
        'nip',
        'nik',
        'nomor_kk',
        'agama',
        'jenis_kelamin',
        'status_pernikahan',
        'jumlah_anak',
        'kota_lahir',
        'tanggal_lahir',
        'alamat',
        'alamat_ktp',
        'nama_ibu_kandung',
        'golongan_darah',
        'nomor_telepon',
        'id_entitas',
        'outlet_id',
        'id_departemen',
        'id_divisi',
        'id_jabatan',
        'id_user',
        'supervisor_id',
        'status_aktif',
        'foto_profil',
        'email',
        'created_by',
    ];



    protected $dates = ['deleted_at'];

    protected $casts = [
        'posisi_awal' => 'array',
    ];


    /**
     * Perhitungan umur berdasarkan tanggal lahir
     */
    public function getUmurAttribute()
    {
        if ($this->tanggal_lahir) {
            return Carbon::parse($this->tanggal_lahir)->age;
        }

        return null;
    }



    // Relationships
    public function jabatan()
    {
        return $this->belongsTo(Jabatan::class, 'id_jabatan');
    }

    public function departemen()
    {
        return $this->belongsTo(Departemen::class, 'id_departemen');
    }

    public function divisi()
    {
        return $this->belongsTo(Divisi::class, 'id_divisi');
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class, 'id_entitas');
    }

    public function outlet()
    {
        return $this->belongsTo(Outlet::class, 'outlet_id');
    }

    public function riwayatKontrak()
    {
        return $this->hasMany(RiwayatKontrak::class);
    }

    public function penggajian()
    {
        return $this->hasMany(PenggajianKaryawan::class);
    }

    public function kerabatDarurat()
    {
        return $this->hasMany(KerabatDarurat::class);
    }

    public function pendidikan()
    {
        return $this->hasMany(PendidikanKaryawan::class);
    }

    public function resignLogs()
    {
        return $this->hasMany(ResignLog::class);
    }

    public function bpjs()
    {
        return $this->hasOne(KaryawanBpjs::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'id_user', 'id');
    }

    public function kpiPenilaians()
    {
        return $this->hasMany(KpiPenilaian::class);
    }

    public function pelanggarans()
    {
        return $this->hasMany(Pelanggaran::class);
    }

    public function pelanggaran()
    {
        return $this->hasMany(Pelanggaran::class);
    }

    public function payrollTransactions()
    {
        return $this->hasMany(PayrollTransaction::class);
    }

    public function dokumens()
    {
        return $this->hasMany(Dokumen::class);
    }

    public function schedules()
    {
        return $this->hasMany(Schedule::class);
    }

    public function mutasiPromosiDemosi()
    {
        return $this->hasMany(\App\Models\MutasiPromosiDemosi::class, 'karyawan_id');
    }

    /**
     * Get the supervisor of this employee
     */
    public function supervisor()
    {
        return $this->belongsTo(User::class, 'supervisor_id');
    }

    /**
     * Get the attendance records for this employee
     */
    public function absensi()
    {
        return $this->hasMany(Absensi::class, 'karyawan_id');
    }

    /**
     * Get the bulk schedules associated with this employee
     */
    public function jadwalMasal()
    {
        return $this->belongsToMany(JadwalMasal::class, 'jadwal_masal_karyawan', 'karyawan_id', 'jadwal_masal_id')
            ->withTimestamps();
    }

    /**
     * Get the leave/permission requests for this employee
     */
    public function cutiIzin()
    {
        return $this->hasMany(\App\Models\CutiIzin::class, 'karyawan_id');
    }

    /**
     * Get the permissions for this employee
     */
    public function permissions()
    {
        return $this->hasMany(\App\Models\KaryawanPermission::class, 'karyawan_id');
    }

    /**
     * Get the overtime records for this employee
     */
    public function lembur()
    {
        return $this->hasMany(\App\Models\Lembur::class, 'karyawan_id');
    }

    /**
     * Get the employee deductions (potongan karyawan)
     */
    public function potonganKaryawan()
    {
        return $this->hasMany(\App\Models\PotonganKaryawan::class, 'karyawan_id');
    }

    // kontrak aktif paling baru
    public function getJenisKontrakAttribute()
    {
        return $this->riwayatKontrak()
            ->where('is_active', 1)
            ->orderBy('tgl_mulai', 'desc')
            ->value('jenis_kontrak');
    }

    
}
