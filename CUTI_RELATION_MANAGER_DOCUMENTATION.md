# Dokumentasi: Cuti Relation Manager untuk PayrollTransaction

## 🎯 **<PERSON>juan**
Membuat relation manager untuk melihat data cuti dan izin karyawan pada halaman PayrollTransaction dalam timeframe antara tanggal mulai dan tanggal cutoff periode payroll.

## 📋 **Fitur yang Diimplementasikan**

### **1. Relasi Database Baru**
Menambahkan relasi `cutiRecords()` di model `PayrollTransaction` untuk mengakses data cuti/izin karyawan.

### **2. Cuti Relation Manager**
Relation manager lengkap dengan fitur:
- **View data cuti/izin** dalam periode payroll
- **Filter** berdasarkan jenis, status, durasi, dokumen
- **Detail informasi** lengkap setiap permohonan cuti
- **Read-only** (tidak bisa edit dari payroll)

## 🔧 **File yang Dibuat/Dimodifikasi**

### **1. Model PayrollTransaction** (`app/Models/PayrollTransaction.php`)

#### **Relasi Baru:**
```php
/**
 * Relasi ke CutiIzin berdasarkan periode dan karyawan
 */
public function cutiRecords()
{
    return $this->hasMany(\App\Models\CutiIzin::class, 'karyawan_id', 'karyawan_id')
        ->with(['karyawan', 'approvedBy']);
}
```

### **2. Cuti Relation Manager** (`app/Filament/Resources/PayrollTransactionResource/RelationManagers/CutiRelationManager.php`)

#### **Fitur Utama:**
- **Relationship**: `cutiRecords`
- **Title**: "Data Cuti & Izin"
- **Mode**: Read-only

#### **Kolom Tabel:**
```php
- Jenis Permohonan (badge: Cuti/Izin/Sakit)
- Periode Cuti (tanggal mulai - selesai)
- Durasi (jumlah hari)
- Status (badge: Pending/Disetujui/Ditolak)
- Disetujui Oleh
- Tanggal Persetujuan
- Alasan
- Dokumen Pendukung (icon)
```

#### **Filter yang Tersedia:**
```php
- Jenis Permohonan (Cuti, Izin, Sakit)
- Status (Pending, Disetujui, Ditolak)
- Hanya yang Disetujui
- Ada Dokumen Pendukung
- Cuti Panjang (>3 hari)
```

#### **Filtering Periode:**
```php
->modifyQueryUsing(function (Builder $query) {
    $payrollTransaction = $this->getOwnerRecord();
    $period = $payrollTransaction->payrollPeriod;
    
    if ($period) {
        $query->where(function ($q) use ($period) {
            // Cuti yang dimulai dalam periode
            $q->whereBetween('tanggal_mulai', [$period->tanggal_mulai, $period->tanggal_cutoff])
              // Atau cuti yang berakhir dalam periode
              ->orWhereBetween('tanggal_selesai', [$period->tanggal_mulai, $period->tanggal_cutoff])
              // Atau cuti yang mencakup seluruh periode
              ->orWhere(function ($q2) use ($period) {
                  $q2->where('tanggal_mulai', '<=', $period->tanggal_mulai)
                     ->where('tanggal_selesai', '>=', $period->tanggal_cutoff);
              });
        });
    }
    
    return $query;
})
```

### **3. PayrollTransactionResource** (`app/Filament/Resources/PayrollTransactionResource.php`)

#### **Update Relations:**
```php
public static function getRelations(): array
{
    return [
        RelationManagers\PayrollDeductionsRelationManager::class,
        RelationManagers\AbsensiRelationManager::class,
        RelationManagers\ScheduleRelationManager::class,
        RelationManagers\CutiRelationManager::class, // ← BARU
    ];
}
```

## 📊 **Tampilan dan Fungsionalitas**

### **1. Tabel Data Cuti/Izin**
```
┌─────────┬─────────────────┬─────────┬─────────────┬─────────────────┬─────────────┐
│ Jenis   │ Periode Cuti    │ Durasi  │ Status      │ Disetujui Oleh  │ Alasan      │
├─────────┼─────────────────┼─────────┼─────────────┼─────────────────┼─────────────┤
│ Cuti    │ 25-27 Aug 2025  │ 3 hari  │ Disetujui   │ John Manager    │ Liburan     │
│ Izin    │ 02 Sep 2025     │ 1 hari  │ Pending     │ -               │ Keperluan   │
│ Sakit   │ 15-16 Sep 2025  │ 2 hari  │ Disetujui   │ Jane Supervisor │ Demam       │
└─────────┴─────────────────┴─────────┴─────────────┴─────────────────┴─────────────┘
```

### **2. Badge Colors**
```php
Jenis Permohonan:
- Cuti: success (hijau)
- Izin: warning (kuning)
- Sakit: danger (merah)

Status:
- Pending: warning (kuning)
- Disetujui: success (hijau)
- Ditolak: danger (merah)
```

### **3. Modal Detail**
Ketika klik "View", menampilkan modal dengan informasi lengkap:
- Jenis Permohonan
- Tanggal Mulai/Selesai
- Jumlah Hari
- Status dan Persetujuan
- Alasan dan Keterangan Tambahan
- Dokumen Pendukung
- Alasan Penolakan (jika ditolak)

## 🎯 **Filtering Periode yang Cerdas**

### **1. Overlap Detection:**
Sistem mendeteksi cuti yang **overlap** dengan periode payroll:

```php
Periode Payroll: 21 Aug - 20 Sep 2025

✅ Ditampilkan:
- Cuti 25-27 Aug (dimulai dalam periode)
- Cuti 15-25 Sep (berakhir dalam periode)  
- Cuti 10 Aug - 30 Sep (mencakup seluruh periode)

❌ Tidak ditampilkan:
- Cuti 10-15 Aug (sebelum periode)
- Cuti 25-30 Sep (setelah periode)
```

### **2. Query Logic:**
```sql
WHERE (
    -- Cuti dimulai dalam periode
    tanggal_mulai BETWEEN '2025-08-21' AND '2025-09-20'
    OR
    -- Cuti berakhir dalam periode
    tanggal_selesai BETWEEN '2025-08-21' AND '2025-09-20'
    OR
    -- Cuti mencakup seluruh periode
    (tanggal_mulai <= '2025-08-21' AND tanggal_selesai >= '2025-09-20')
)
```

## 🎯 **Keunggulan**

### **1. Konsistensi dengan Existing Pattern**
- Mengikuti pattern yang sama dengan relation manager lainnya
- Struktur file dan naming convention konsisten
- UI/UX yang familiar

### **2. Informasi Lengkap**
- Menampilkan semua data cuti yang relevan
- Filter yang berguna untuk analisis
- Integrasi dengan data approval dan dokumen

### **3. Smart Period Filtering**
- Mendeteksi overlap cuti dengan periode payroll
- Tidak hanya berdasarkan tanggal mulai
- Menangkap cuti yang mencakup periode

### **4. User Experience**
- Read-only untuk mencegah konflik data
- Visual indicators (badges, icons)
- Responsive table design
- Empty state yang informatif

## 📈 **Manfaat untuk User**

### **1. Untuk HR/Admin:**
- **Verifikasi cuti** karyawan dalam periode payroll
- **Analisis pola cuti** (jenis, durasi, frekuensi)
- **Cross-check** antara cuti dan absensi
- **Audit trail** untuk persetujuan cuti

### **2. Untuk Manager:**
- **Review cuti** tim dalam periode tertentu
- **Identifikasi** cuti yang mempengaruhi produktivitas
- **Monitoring** penggunaan cuti karyawan
- **Validasi** kesesuaian cuti dengan payroll

### **3. Untuk Payroll Processing:**
- **Konfirmasi** potongan cuti yang dihitung
- **Verifikasi** basis perhitungan hari kerja
- **Audit** sumber data untuk payroll calculation
- **Troubleshooting** discrepancy antara cuti dan gaji

## 🔄 **Integrasi dengan Fitur Existing**

### **1. Dengan Data Absensi:**
- Komplementer untuk analisis kehadiran
- Membantu identifikasi gap antara cuti dan absensi
- Support untuk troubleshooting attendance issues

### **2. Dengan Jadwal Kerja:**
- Cross-reference dengan jadwal yang dibuat
- Validasi cuti terhadap jadwal kerja
- Analisis impact cuti terhadap operasional

### **3. Dengan Payroll Deductions:**
- Context untuk potongan cuti melebihi kuota
- Basis untuk perhitungan pengurangan gaji
- Reference untuk penalty calculations

## ✅ **Status Implementasi**

### **Completed:**
- ✅ Relasi database `cutiRecords()`
- ✅ `CutiRelationManager` lengkap
- ✅ Integration dengan `PayrollTransactionResource`
- ✅ Smart period filtering
- ✅ Testing dan validasi

### **Ready to Use:**
- ✅ Dapat diakses di halaman view PayrollTransaction
- ✅ Tab "Data Cuti & Izin" tersedia
- ✅ Semua fitur filter dan view berfungsi
- ✅ Compatible dengan existing data structure

## 🛠️ **Fix Component Loading Issue**

### **Masalah:**
```
Unable to find component: [app.filament.resources.payroll-transaction-resource.relation-managers.cuti-relation-manager]
```

### **Solusi:**
1. **Simplify Model Relation** - Hapus complex dynamic query dari model
2. **Use Table-level Filtering** - Pindahkan filtering ke `modifyQueryUsing()`
3. **Clear Cache** - `php artisan optimize:clear`

## 🎉 **Kesimpulan**

**Cuti Relation Manager** telah berhasil diimplementasikan dan siap digunakan! Fitur ini memberikan visibilitas lengkap terhadap data cuti dan izin karyawan dalam periode payroll, melengkapi informasi yang sudah ada dari relation manager lainnya.

**Key Benefits:**
- 📅 **Visibilitas cuti** dalam periode payroll
- 🔍 **Filter dan search** yang powerful
- 📊 **Data lengkap** dengan relationships
- 🔒 **Read-only** untuk data integrity
- 🎨 **UI konsisten** dengan existing pattern
- 🧠 **Smart filtering** untuk overlap detection

Sekarang user dapat dengan mudah melihat dan menganalisis data cuti karyawan langsung dari halaman PayrollTransaction! 🚀
