<?php

namespace App\Filament\Resources\PayrollTransactionResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class CutiRelationManager extends RelationManager
{
    protected static string $relationship = 'cutiRecords';

    protected static ?string $title = 'Data Cuti & Izin';

    protected static ?string $recordTitleAttribute = 'jenis_permohonan';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Cuti/Izin')
                    ->schema([
                        Forms\Components\Select::make('jenis_permohonan')
                            ->label('Jenis Permohonan')
                            ->options([
                                'cuti' => 'Cuti',
                                'izin' => 'Izin',
                                'sakit' => 'Sakit',
                            ])
                            ->required()
                            ->disabled(),

                        Forms\Components\DatePicker::make('tanggal_mulai')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->disabled(),

                        Forms\Components\DatePicker::make('tanggal_selesai')
                            ->label('Tanggal Selesai')
                            ->required()
                            ->disabled(),

                        Forms\Components\TextInput::make('jumlah_hari')
                            ->label('Jumlah Hari')
                            ->numeric()
                            ->disabled(),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'pending' => 'Pending',
                                'approved' => 'Disetujui',
                                'rejected' => 'Ditolak',
                            ])
                            ->disabled(),

                        Forms\Components\Select::make('approved_by')
                            ->label('Disetujui Oleh')
                            ->relationship('approvedBy', 'name')
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Detail Permohonan')
                    ->schema([
                        Forms\Components\Textarea::make('alasan')
                            ->label('Alasan')
                            ->disabled()
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('keterangan_tambahan')
                            ->label('Keterangan Tambahan')
                            ->disabled()
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->disabled()
                            ->visible(fn($record) => $record && $record->status === 'rejected')
                            ->columnSpanFull(),

                        Forms\Components\FileUpload::make('dokumen_pendukung')
                            ->label('Dokumen Pendukung')
                            ->disabled()
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('jenis_permohonan')
            ->modifyQueryUsing(function (Builder $query) {
                $payrollTransaction = $this->getOwnerRecord();
                $period = $payrollTransaction->payrollPeriod;
                
                if ($period) {
                    $query->where(function ($q) use ($period) {
                        // Cuti yang dimulai dalam periode
                        $q->whereBetween('tanggal_mulai', [$period->tanggal_mulai, $period->tanggal_cutoff])
                          // Atau cuti yang berakhir dalam periode
                          ->orWhereBetween('tanggal_selesai', [$period->tanggal_mulai, $period->tanggal_cutoff])
                          // Atau cuti yang mencakup seluruh periode
                          ->orWhere(function ($q2) use ($period) {
                              $q2->where('tanggal_mulai', '<=', $period->tanggal_mulai)
                                 ->where('tanggal_selesai', '>=', $period->tanggal_cutoff);
                          });
                    });
                }
                
                return $query;
            })
            ->columns([
                TextColumn::make('jenis_permohonan')
                    ->label('Jenis')
                    ->badge()
                    ->color(function ($state) {
                        return match ($state) {
                            'cuti' => 'success',
                            'izin' => 'warning',
                            'sakit' => 'danger',
                            default => 'gray'
                        };
                    })
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            'cuti' => 'Cuti',
                            'izin' => 'Izin',
                            'sakit' => 'Sakit',
                            default => ucfirst($state)
                        };
                    }),

                TextColumn::make('periode_cuti')
                    ->label('Periode Cuti')
                    ->getStateUsing(function ($record) {
                        $mulai = Carbon::parse($record->tanggal_mulai)->format('d M Y');
                        $selesai = Carbon::parse($record->tanggal_selesai)->format('d M Y');
                        
                        if ($record->tanggal_mulai === $record->tanggal_selesai) {
                            return $mulai;
                        }
                        
                        return "{$mulai} - {$selesai}";
                    })
                    ->description(function ($record) {
                        $mulai = Carbon::parse($record->tanggal_mulai);
                        $selesai = Carbon::parse($record->tanggal_selesai);
                        
                        if ($mulai->isSameDay($selesai)) {
                            return $mulai->format('l');
                        }
                        
                        return $mulai->format('l') . ' - ' . $selesai->format('l');
                    }),

                TextColumn::make('jumlah_hari')
                    ->label('Durasi')
                    ->suffix(' hari')
                    ->alignCenter()
                    ->color('primary'),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(function ($state) {
                        return match ($state) {
                            'pending' => 'warning',
                            'approved' => 'success',
                            'rejected' => 'danger',
                            default => 'gray'
                        };
                    })
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            'pending' => 'Pending',
                            'approved' => 'Disetujui',
                            'rejected' => 'Ditolak',
                            default => ucfirst($state)
                        };
                    }),

                TextColumn::make('approvedBy.name')
                    ->label('Disetujui Oleh')
                    ->limit(15)
                    ->placeholder('Belum disetujui')
                    ->tooltip(function ($record) {
                        return $record->approvedBy?->name;
                    }),

                TextColumn::make('approved_at')
                    ->label('Tanggal Persetujuan')
                    ->dateTime('d M Y H:i')
                    ->placeholder('Belum disetujui')
                    ->color('success'),

                TextColumn::make('alasan')
                    ->label('Alasan')
                    ->limit(30)
                    ->tooltip(function ($record) {
                        return $record->alasan;
                    }),

                IconColumn::make('dokumen_pendukung')
                    ->label('Dokumen')
                    ->boolean()
                    ->trueIcon('heroicon-o-document-text')
                    ->falseIcon('heroicon-o-minus')
                    ->trueColor('success')
                    ->falseColor('gray')
                    ->getStateUsing(function ($record) {
                        return !empty($record->dokumen_pendukung);
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_permohonan')
                    ->label('Jenis Permohonan')
                    ->options([
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                    ]),

                Tables\Filters\Filter::make('approved_only')
                    ->label('Hanya yang Disetujui')
                    ->query(fn (Builder $query): Builder => $query->where('status', 'approved')),

                Tables\Filters\Filter::make('with_document')
                    ->label('Ada Dokumen Pendukung')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('dokumen_pendukung')),

                Tables\Filters\Filter::make('long_leave')
                    ->label('Cuti Panjang (>3 hari)')
                    ->query(fn (Builder $query): Builder => $query->where('jumlah_hari', '>', 3)),
            ])
            ->headerActions([
                // No create action - leave requests are managed separately
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Cuti/Izin')
                    ->modalWidth('2xl'),
            ])
            ->bulkActions([
                // No bulk actions
            ])
            ->defaultSort('tanggal_mulai', 'desc')
            ->emptyStateHeading('Tidak Ada Data Cuti/Izin')
            ->emptyStateDescription('Tidak ada data cuti atau izin untuk periode payroll ini.')
            ->emptyStateIcon('heroicon-o-calendar-days');
    }

    public function isReadOnly(): bool
    {
        return true; // Read-only - leave requests are managed in separate module
    }
}
