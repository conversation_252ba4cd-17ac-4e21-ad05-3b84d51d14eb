<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('shift', function (Blueprint $table) {
            // Add is_beda_hari field to track shifts that span across different days
            if (!Schema::hasColumn('shift', 'is_beda_hari')) {
                $table->boolean('is_beda_hari')->default(false)->after('is_split_shift')
                    ->comment('True if shift end time is on the next day (overnight shift)');
            }
            
            // Add index for better performance
            $table->index(['is_beda_hari', 'is_active'], 'idx_shift_beda_hari_active');
        });
    }

    public function down(): void
    {
        Schema::table('shift', function (Blueprint $table) {
            $table->dropIndex('idx_shift_beda_hari_active');
            
            if (Schema::hasColumn('shift', 'is_beda_hari')) {
                $table->dropColumn('is_beda_hari');
            }
        });
    }
};
