<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('absensi', function (Blueprint $table) {
            // Add entitas_id column after jadwal_id
            if (!Schema::hasColumn('absensi', 'entitas_id')) {
                $table->unsignedBigInteger('entitas_id')->nullable()->after('jadwal_id')
                    ->comment('ID entitas tempat absensi dilakukan');
                
                $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('set null');
                $table->index('entitas_id', 'idx_absensi_entitas');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('absensi', function (Blueprint $table) {
            if (Schema::hasColumn('absensi', 'entitas_id')) {
                $table->dropForeign(['entitas_id']);
                $table->dropIndex('idx_absensi_entitas');
                $table->dropColumn('entitas_id');
            }
        });
    }
};
