<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.warehouse.resources.product-resource.pages.create-product' => 'App\\Filament\\Warehouse\\Resources\\ProductResource\\Pages\\CreateProduct',
    'app.filament.warehouse.resources.product-resource.pages.edit-product' => 'App\\Filament\\Warehouse\\Resources\\ProductResource\\Pages\\EditProduct',
    'app.filament.warehouse.resources.product-resource.pages.list-products' => 'App\\Filament\\Warehouse\\Resources\\ProductResource\\Pages\\ListProducts',
    'app.filament.warehouse.resources.product-resource.pages.view-product' => 'App\\Filament\\Warehouse\\Resources\\ProductResource\\Pages\\ViewProduct',
    'app.filament.warehouse.resources.purchase-order-resource.pages.create-purchase-order' => 'App\\Filament\\Warehouse\\Resources\\PurchaseOrderResource\\Pages\\CreatePurchaseOrder',
    'app.filament.warehouse.resources.purchase-order-resource.pages.edit-purchase-order' => 'App\\Filament\\Warehouse\\Resources\\PurchaseOrderResource\\Pages\\EditPurchaseOrder',
    'app.filament.warehouse.resources.purchase-order-resource.pages.list-purchase-orders' => 'App\\Filament\\Warehouse\\Resources\\PurchaseOrderResource\\Pages\\ListPurchaseOrders',
    'app.filament.warehouse.resources.purchase-order-resource.pages.view-purchase-order' => 'App\\Filament\\Warehouse\\Resources\\PurchaseOrderResource\\Pages\\ViewPurchaseOrder',
    'app.filament.warehouse.resources.sales-order-resource.pages.create-sales-order' => 'App\\Filament\\Warehouse\\Resources\\SalesOrderResource\\Pages\\CreateSalesOrder',
    'app.filament.warehouse.resources.sales-order-resource.pages.edit-sales-order' => 'App\\Filament\\Warehouse\\Resources\\SalesOrderResource\\Pages\\EditSalesOrder',
    'app.filament.warehouse.resources.sales-order-resource.pages.list-sales-orders' => 'App\\Filament\\Warehouse\\Resources\\SalesOrderResource\\Pages\\ListSalesOrders',
    'app.filament.warehouse.resources.sales-order-resource.pages.view-sales-order' => 'App\\Filament\\Warehouse\\Resources\\SalesOrderResource\\Pages\\ViewSalesOrder',
    'app.filament.warehouse.resources.stock-adjustment-resource.pages.create-stock-adjustment' => 'App\\Filament\\Warehouse\\Resources\\StockAdjustmentResource\\Pages\\CreateStockAdjustment',
    'app.filament.warehouse.resources.stock-adjustment-resource.pages.edit-stock-adjustment' => 'App\\Filament\\Warehouse\\Resources\\StockAdjustmentResource\\Pages\\EditStockAdjustment',
    'app.filament.warehouse.resources.stock-adjustment-resource.pages.list-stock-adjustments' => 'App\\Filament\\Warehouse\\Resources\\StockAdjustmentResource\\Pages\\ListStockAdjustments',
    'app.filament.warehouse.resources.stock-adjustment-resource.pages.view-stock-adjustment' => 'App\\Filament\\Warehouse\\Resources\\StockAdjustmentResource\\Pages\\ViewStockAdjustment',
    'app.filament.warehouse.resources.stock-movement-resource.pages.create-stock-movement' => 'App\\Filament\\Warehouse\\Resources\\StockMovementResource\\Pages\\CreateStockMovement',
    'app.filament.warehouse.resources.stock-movement-resource.pages.edit-stock-movement' => 'App\\Filament\\Warehouse\\Resources\\StockMovementResource\\Pages\\EditStockMovement',
    'app.filament.warehouse.resources.stock-movement-resource.pages.list-stock-movements' => 'App\\Filament\\Warehouse\\Resources\\StockMovementResource\\Pages\\ListStockMovements',
    'app.filament.warehouse.resources.stock-movement-resource.pages.view-stock-movement' => 'App\\Filament\\Warehouse\\Resources\\StockMovementResource\\Pages\\ViewStockMovement',
    'app.filament.warehouse.resources.warehouse-resource.pages.create-warehouse' => 'App\\Filament\\Warehouse\\Resources\\WarehouseResource\\Pages\\CreateWarehouse',
    'app.filament.warehouse.resources.warehouse-resource.pages.edit-warehouse' => 'App\\Filament\\Warehouse\\Resources\\WarehouseResource\\Pages\\EditWarehouse',
    'app.filament.warehouse.resources.warehouse-resource.pages.list-warehouses' => 'App\\Filament\\Warehouse\\Resources\\WarehouseResource\\Pages\\ListWarehouses',
    'app.filament.warehouse.resources.warehouse-resource.pages.view-warehouse' => 'App\\Filament\\Warehouse\\Resources\\WarehouseResource\\Pages\\ViewWarehouse',
    'app.filament.warehouse.pages.dashboard' => 'App\\Filament\\Warehouse\\Pages\\Dashboard',
    'app.filament.warehouse.pages.movement-history-report' => 'App\\Filament\\Warehouse\\Pages\\MovementHistoryReport',
    'app.filament.warehouse.pages.stock-overview' => 'App\\Filament\\Warehouse\\Pages\\StockOverview',
    'app.filament.warehouse.pages.stock-report' => 'App\\Filament\\Warehouse\\Pages\\StockReport',
    'app.filament.warehouse.widgets.active-warehouses-widget' => 'App\\Filament\\Warehouse\\Widgets\\ActiveWarehousesWidget',
    'app.filament.warehouse.widgets.low-stock-alerts-widget' => 'App\\Filament\\Warehouse\\Widgets\\LowStockAlertsWidget',
    'app.filament.warehouse.widgets.pending-orders-widget' => 'App\\Filament\\Warehouse\\Widgets\\PendingOrdersWidget',
    'app.filament.warehouse.widgets.stock-movement-chart' => 'App\\Filament\\Warehouse\\Widgets\\StockMovementChart',
    'app.filament.warehouse.widgets.total-products-widget' => 'App\\Filament\\Warehouse\\Widgets\\TotalProductsWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Pages\\Dashboard.php' => 'App\\Filament\\Warehouse\\Pages\\Dashboard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Pages\\MovementHistoryReport.php' => 'App\\Filament\\Warehouse\\Pages\\MovementHistoryReport',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Pages\\StockOverview.php' => 'App\\Filament\\Warehouse\\Pages\\StockOverview',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Pages\\StockReport.php' => 'App\\Filament\\Warehouse\\Pages\\StockReport',
    0 => 'App\\Filament\\Warehouse\\Pages\\Dashboard',
    1 => 'App\\Filament\\Warehouse\\Pages\\StockOverview',
    2 => 'App\\Filament\\Warehouse\\Pages\\StockReport',
    3 => 'App\\Filament\\Warehouse\\Pages\\MovementHistoryReport',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Warehouse/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Warehouse\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Resources\\ProductResource.php' => 'App\\Filament\\Warehouse\\Resources\\ProductResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Resources\\PurchaseOrderResource.php' => 'App\\Filament\\Warehouse\\Resources\\PurchaseOrderResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Resources\\SalesOrderResource.php' => 'App\\Filament\\Warehouse\\Resources\\SalesOrderResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Resources\\StockAdjustmentResource.php' => 'App\\Filament\\Warehouse\\Resources\\StockAdjustmentResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Resources\\StockMovementResource.php' => 'App\\Filament\\Warehouse\\Resources\\StockMovementResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Resources\\WarehouseResource.php' => 'App\\Filament\\Warehouse\\Resources\\WarehouseResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Warehouse/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Warehouse\\Resources',
  ),
  'widgets' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Widgets\\ActiveWarehousesWidget.php' => 'App\\Filament\\Warehouse\\Widgets\\ActiveWarehousesWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Widgets\\LowStockAlertsWidget.php' => 'App\\Filament\\Warehouse\\Widgets\\LowStockAlertsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Widgets\\PendingOrdersWidget.php' => 'App\\Filament\\Warehouse\\Widgets\\PendingOrdersWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Widgets\\StockMovementChart.php' => 'App\\Filament\\Warehouse\\Widgets\\StockMovementChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Warehouse\\Widgets\\TotalProductsWidget.php' => 'App\\Filament\\Warehouse\\Widgets\\TotalProductsWidget',
    0 => 'App\\Filament\\Warehouse\\Widgets\\TotalProductsWidget',
    1 => 'App\\Filament\\Warehouse\\Widgets\\ActiveWarehousesWidget',
    2 => 'App\\Filament\\Warehouse\\Widgets\\LowStockAlertsWidget',
    3 => 'App\\Filament\\Warehouse\\Widgets\\PendingOrdersWidget',
    4 => 'App\\Filament\\Warehouse\\Widgets\\StockMovementChart',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Warehouse/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Warehouse\\Widgets',
  ),
);