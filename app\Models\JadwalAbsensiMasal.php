<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class JadwalAbsensiMasal extends Model
{
    use HasFactory;

    protected $table = 'jadwal_absensi_masal';

    protected $fillable = [
        'nama_jadwal',
        'tanggal_mulai',
        'tanggal_selesai',
        'shift_id',
        'entitas_id',
        'created_by',
        'keterangan',
        'generated_at',
        'status',
    ];

    protected $casts = [
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'generated_at' => 'datetime',
    ];

    /**
     * Get the shift associated with this bulk schedule
     */
    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    /**
     * Get the user who created this bulk schedule
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the entitas/entity for this bulk schedule
     */
    public function entitas()
    {
        return $this->belongsTo(Entitas::class, 'entitas_id');
    }

    /**
     * Many-to-many relationship with Karyawan
     */
    public function karyawan()
    {
        return $this->belongsToMany(
            Karyawan::class,
            'jadwal_absensi_masal_karyawan',
            'jadwal_absensi_masal_id',
            'karyawan_id'
        )->withTimestamps();
    }

    /**
     * Generate schedules and attendance records for all assigned employees
     */
    public function generateJadwalDanAbsensi(): array
    {
        $results = [
            'success' => 0,
            'errors' => [],
            'schedules_created' => 0,
            'attendance_created' => 0,
        ];

        if ($this->generated_at) {
            $results['errors'][] = 'Jadwal dan absensi sudah pernah di-generate sebelumnya.';
            return $results;
        }

        $shift = $this->shift;
        if (!$shift) {
            $results['errors'][] = 'Shift tidak ditemukan.';
            return $results;
        }

        $karyawanIds = $this->karyawan()->pluck('karyawan.id');
        if ($karyawanIds->isEmpty()) {
            $results['errors'][] = 'Tidak ada karyawan yang dipilih.';
            return $results;
        }

        $startDate = Carbon::parse($this->tanggal_mulai);
        $endDate = Carbon::parse($this->tanggal_selesai);
        $createdBy = $this->created_by;
        $entitasId = $this->entitas_id;

        // Loop through each date in the range
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $dateString = $date->format('Y-m-d');

            // Loop through each employee
            foreach ($karyawanIds as $karyawanId) {
                try {
                    // Create schedule
                    $scheduleData = [
                        'karyawan_id' => $karyawanId,
                        'shift_id' => $shift->id,
                        'entitas_id' => $entitasId,
                        'supervisor_id' => $createdBy,
                        'tanggal_jadwal' => $dateString,
                        'waktu_masuk' => $shift->waktu_mulai,
                        'waktu_keluar' => $shift->waktu_selesai,
                        'is_approved' => true,
                    ];

                    $schedule = Schedule::updateOrCreate(
                        [
                            'karyawan_id' => $karyawanId,
                            'tanggal_jadwal' => $dateString,
                        ],
                        $scheduleData
                    );

                    $results['schedules_created']++;

                    // Create attendance record with auto-populated data
                    $attendanceData = [
                        'karyawan_id' => $karyawanId,
                        'jadwal_id' => $schedule->id,
                        'entitas_id' => $entitasId,
                        'tanggal_absensi' => $dateString,
                        'waktu_masuk' => $shift->waktu_mulai,
                        'waktu_keluar' => $shift->waktu_selesai,
                        'status' => 'hadir',
                        'keterangan' => 'Auto-generated dari jadwal absensi masal',
                    ];

                    // Handle split shift
                    if ($shift->isSplitShift()) {
                        // Create attendance for period 1
                        $attendanceData['periode'] = 1;
                        $attendanceData['waktu_keluar'] = $shift->waktu_selesai; // First period end time

                        Absensi::updateOrCreate(
                            [
                                'karyawan_id' => $karyawanId,
                                'tanggal_absensi' => $dateString,
                                'periode' => 1,
                            ],
                            $attendanceData
                        );

                        // Create attendance for period 2
                        $attendanceData['periode'] = 2;
                        $attendanceData['waktu_masuk'] = $shift->waktu_mulai_periode2;
                        $attendanceData['waktu_keluar'] = $shift->waktu_selesai_periode2;

                        Absensi::updateOrCreate(
                            [
                                'karyawan_id' => $karyawanId,
                                'tanggal_absensi' => $dateString,
                                'periode' => 2,
                            ],
                            $attendanceData
                        );

                        $results['attendance_created'] += 2;
                    } else {
                        // Regular shift
                        $attendanceData['periode'] = 1;

                        Absensi::updateOrCreate(
                            [
                                'karyawan_id' => $karyawanId,
                                'tanggal_absensi' => $dateString,
                                'periode' => 1,
                            ],
                            $attendanceData
                        );

                        $results['attendance_created']++;
                    }

                    $results['success']++;
                } catch (\Exception $e) {
                    $results['errors'][] = "Error untuk karyawan ID {$karyawanId} pada {$dateString}: " . $e->getMessage();
                }
            }
        }

        // Mark as generated
        $this->update([
            'generated_at' => now(),
            'status' => 'completed',
        ]);

        return $results;
    }

    /**
     * Check if this bulk schedule has been generated
     */
    public function isGenerated(): bool
    {
        return !is_null($this->generated_at);
    }

    /**
     * Get total days in the date range
     */
    public function getTotalDaysAttribute(): int
    {
        $startDate = Carbon::parse($this->tanggal_mulai);
        $endDate = Carbon::parse($this->tanggal_selesai);
        return $startDate->diffInDays($endDate) + 1;
    }

    /**
     * Get total expected records (schedules + attendance)
     */
    public function getTotalExpectedRecordsAttribute(): int
    {
        $totalDays = $this->total_days;
        $totalEmployees = $this->karyawan()->count();
        $multiplier = $this->shift && $this->shift->isSplitShift() ? 3 : 2; // 1 schedule + 1 or 2 attendance records
        
        return $totalDays * $totalEmployees * $multiplier;
    }
}
