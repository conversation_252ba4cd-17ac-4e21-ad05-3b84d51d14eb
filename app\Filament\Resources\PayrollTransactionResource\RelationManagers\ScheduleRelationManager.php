<?php

namespace App\Filament\Resources\PayrollTransactionResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;

use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ScheduleRelationManager extends RelationManager
{
    protected static string $relationship = 'scheduleRecords';

    protected static ?string $title = 'Jadwal Kerja';

    protected static ?string $recordTitleAttribute = 'tanggal_jadwal';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Jadwal')
                    ->schema([
                        Forms\Components\DatePicker::make('tanggal_jadwal')
                            ->label('Tanggal Jadwal')
                            ->required()
                            ->disabled(),

                        Forms\Components\Select::make('shift_id')
                            ->label('Shift')
                            ->relationship('shift', 'nama_shift')
                            ->disabled(),

                        Forms\Components\Select::make('entitas_id')
                            ->label('Entitas/Lokasi')
                            ->relationship('entitas', 'nama')
                            ->disabled(),

                        Forms\Components\TimePicker::make('waktu_masuk')
                            ->label('Waktu Masuk')
                            ->disabled(),

                        Forms\Components\TimePicker::make('waktu_keluar')
                            ->label('Waktu Keluar')
                            ->disabled(),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'scheduled' => 'Terjadwal',
                                'aktif' => 'Aktif',
                                'libur' => 'Libur',
                                'cuti' => 'Cuti',
                                'completed' => 'Selesai',
                            ])
                            ->disabled(),

                        Forms\Components\Toggle::make('is_approved')
                            ->label('Disetujui')
                            ->disabled(),

                        Forms\Components\Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->disabled()
                            ->columnSpanFull(),
                    ])->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('tanggal_jadwal')
            ->modifyQueryUsing(function (Builder $query) {
                $payrollTransaction = $this->getOwnerRecord();
                $period = $payrollTransaction->payrollPeriod;

                if ($period) {
                    $query->whereBetween('tanggal_jadwal', [
                        $period->tanggal_mulai,
                        $period->tanggal_cutoff
                    ]);
                }

                return $query;
            })
            ->columns([
                TextColumn::make('tanggal_jadwal')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable()
                    ->description(function ($record) {
                        return Carbon::parse($record->tanggal_jadwal)->format('l'); // Nama hari
                    }),

                TextColumn::make('shift.nama_shift')
                    ->label('Shift')
                    ->badge()
                    ->color(function ($record) {
                        return match ($record->shift?->nama_shift) {
                            'Pagi' => 'success',
                            'Siang' => 'warning',
                            'Malam' => 'danger',
                            default => 'gray'
                        };
                    }),

                TextColumn::make('waktu_kerja')
                    ->label('Waktu Kerja')
                    ->getStateUsing(function ($record) {
                        if ($record->waktu_masuk && $record->waktu_keluar) {
                            $masuk = Carbon::parse($record->waktu_masuk)->format('H:i');
                            $keluar = Carbon::parse($record->waktu_keluar)->format('H:i');
                            return "{$masuk} - {$keluar}";
                        }
                        return '-';
                    }),

                TextColumn::make('entitas.nama')
                    ->label('Lokasi/Entitas')
                    ->limit(20)
                    ->tooltip(function ($record) {
                        return $record->entitas?->nama;
                    }),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(function ($state) {
                        return match ($state) {
                            'scheduled' => 'primary',
                            'aktif' => 'success',
                            'libur' => 'warning',
                            'cuti' => 'info',
                            'completed' => 'gray',
                            default => 'gray'
                        };
                    })
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            'scheduled' => 'Terjadwal',
                            'aktif' => 'Aktif',
                            'libur' => 'Libur',
                            'cuti' => 'Cuti',
                            'completed' => 'Selesai',
                            default => ucfirst($state)
                        };
                    }),

                IconColumn::make('is_approved')
                    ->label('Disetujui')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                TextColumn::make('supervisor.name')
                    ->label('Supervisor')
                    ->limit(15)
                    ->placeholder('Tidak ada'),

                TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(30)
                    ->placeholder('Tidak ada')
                    ->tooltip(function ($record) {
                        return $record->keterangan;
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'scheduled' => 'Terjadwal',
                        'aktif' => 'Aktif',
                        'libur' => 'Libur',
                        'cuti' => 'Cuti',
                        'completed' => 'Selesai',
                    ]),

                Tables\Filters\SelectFilter::make('shift_id')
                    ->label('Shift')
                    ->relationship('shift', 'nama_shift'),

                Tables\Filters\SelectFilter::make('entitas_id')
                    ->label('Entitas/Lokasi')
                    ->relationship('entitas', 'nama'),

                Tables\Filters\Filter::make('is_approved')
                    ->label('Hanya yang Disetujui')
                    ->query(fn(Builder $query): Builder => $query->where('is_approved', true)),

                Tables\Filters\Filter::make('weekend')
                    ->label('Akhir Pekan')
                    ->query(function (Builder $query): Builder {
                        return $query->whereRaw('DAYOFWEEK(tanggal_jadwal) IN (1, 7)'); // Sunday = 1, Saturday = 7
                    }),
            ])
            ->headerActions([
                // No create action - schedules are managed separately
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Jadwal Kerja')
                    ->modalWidth('2xl'),
            ])
            ->bulkActions([
                // No bulk actions
            ])
            ->defaultSort('tanggal_jadwal', 'asc')
            ->emptyStateHeading('Tidak Ada Jadwal Kerja')
            ->emptyStateDescription('Tidak ada jadwal kerja untuk periode payroll ini.')
            ->emptyStateIcon('heroicon-o-calendar-days');
    }

    public function isReadOnly(): bool
    {
        return true; // Read-only - schedules are managed in separate module
    }
}
