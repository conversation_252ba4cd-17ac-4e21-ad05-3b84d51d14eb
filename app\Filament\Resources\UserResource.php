<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\UserResource\Pages;
use Illuminate\Support\Facades\Hash;
use App\Traits\HasExportActions;
use App\Exports\UserExport;

class UserResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Users';

    protected static ?string $slug = 'user-list';

    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->role === 'admin' || $user->hasRole('super_admin');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label('Nama Pengguna')
                    ->required(),

                TextInput::make('email')
                    ->label('Email')
                    ->email()
                    ->required()
                    ->unique(ignoreRecord: true),

                TextInput::make('password')
                    ->label('Password')
                    ->password()
                    ->required(fn($record) => ! $record)
                    ->dehydrated(fn($state) => filled($state))
                    ->dehydrateStateUsing(fn($state) => Hash::make($state)),

                Forms\Components\Select::make('role')
                    ->label('Role Absensi')
                    ->options(\App\Enums\UserRole::options())
                    ->required()
                    ->default(\App\Enums\UserRole::KARYAWAN->value),

                Forms\Components\Select::make('shield_roles')
                    ->label('Role Shield')
                    ->multiple()
                    ->options(function () {
                        return \Spatie\Permission\Models\Role::all()
                            ->pluck('name', 'name')
                            ->map(function ($name) {
                                return ucwords(str_replace('_', ' ', $name));
                            })
                            ->toArray();
                    })
                    ->searchable()
                    ->preload()
                    ->helperText('Pilih satu atau lebih role Shield untuk user ini.')
                    ->dehydrated(false) // Don't save to database directly
                    ->afterStateHydrated(function ($component, $record) {
                        // Load current roles when editing
                        if ($record) {
                            $component->state($record->getRoleNames()->toArray());
                        }
                    }),

                Forms\Components\Placeholder::make('karyawan_info')
                    ->label('Karyawan Terkait')
                    ->content(function ($record) {
                        if (!$record) {
                            return 'User baru - belum ada karyawan terkait';
                        }

                        $karyawan = \App\Models\Karyawan::where('id_user', $record->id)->first();

                        if ($karyawan) {
                            return "✅ {$karyawan->nama_lengkap} (NIP: {$karyawan->nip})";
                        }

                        return '❌ Belum terkait dengan karyawan';
                    })
                    ->helperText('Semua user (admin, supervisor, karyawan) dapat dikaitkan dengan data karyawan. Gunakan tombol "Kaitkan dengan Karyawan" atau "Putuskan Kaitan" di halaman edit user.'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Pengguna')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->copyMessage('Email address copied')
                    ->copyMessageDuration(1500),


                TextColumn::make('karyawan.status_aktif')
                    ->label('Status')
                    ->formatStateUsing(function ($state, $record) {
                        if ($state == 1) {
                            return "Aktif";
                        } else if ($state == "") {
                            return "kosong";
                        } else {
                            return "Non Aktif";
                        }
                    }),


                TextColumn::make('role')
                    ->label('Role Absensi')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'admin' => 'danger',
                        'supervisor' => 'warning',
                        'manager' => 'info',
                        'keptok' => 'purple',
                        'karyawan' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => ucfirst($state))
                    ->sortable(),

                // tampilkan jabatan user, kalau usernya ada linknya
                TextColumn::make('karyawan.jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Tidak ada')
                    ->formatStateUsing(fn($state) => ucwords(strtolower($state))),

                TextColumn::make('roles_display')
                    ->label('Role Shield')
                    ->badge()
                    ->color('info')
                    ->getStateUsing(function ($record) {
                        $roles = $record->getRoleNames();
                        if ($roles->isEmpty()) {
                            return ['Tidak ada'];
                        }
                        return $roles->map(function ($role) {
                            return ucwords(str_replace('_', ' ', $role));
                        })->toArray();
                    })
                    ->placeholder('Tidak ada')
                    ->searchable(query: function ($query, $search) {
                        return $query->whereHas('roles', function ($q) use ($search) {
                            $q->where('name', 'like', "%{$search}%");
                        });
                    }),

                TextColumn::make('karyawan.nama_lengkap')
                    ->label('Karyawan Terkait')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Tidak ada')
                    ->formatStateUsing(function ($state, $record) {
                        if (!$state) {
                            return '❌ Belum terkait';
                        }

                        // Get the karyawan's NIP for additional info
                        $karyawan = $record->karyawan;
                        return $state;
                    })
                    ->color(function ($state) {
                        if (!$state) {
                            return 'warning'; // Yellow for users without link
                        }
                        if ($state) {
                            return 'success'; // Green for linked
                        }
                        return 'gray';
                    }),

                TextColumn::make('created_at')
                    ->label('Tanggal Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->label('Role')
                    ->options([
                        'admin' => 'Admin',
                        'supervisor' => 'Supervisor',
                        'karyawan' => 'Karyawan',
                        'manager' => 'Manager',
                        'keptok' => 'Kepala Toko',
                    ]),

                Tables\Filters\SelectFilter::make('karyawan_status')
                    ->label('Status Karyawan')
                    ->options([
                        'linked' => 'Sudah Terkait',
                        'unlinked' => 'Belum Terkait',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'] === 'linked',
                            fn(Builder $query): Builder => $query->whereHas('karyawan'),
                        )->when(
                            $data['value'] === 'unlinked',
                            fn(Builder $query): Builder => $query->whereDoesntHave('karyawan'),
                        );
                    }),

                // filter role absensi dan jabatan
                Tables\Filters\SelectFilter::make('karyawan.jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->options(function () {
                        return \App\Models\Jabatan::all()
                            ->pluck('nama_jabatan', 'id')
                            ->map(function ($name) {
                                return ucwords(strtolower($name));
                            })
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn(Builder $query): Builder => $query->whereHas('karyawan', function ($q) use ($data) {
                                $q->where('id_jabatan', $data['value']);
                            })
                        );
                    }),

                Tables\Filters\SelectFilter::make('karyawan.departemen.nama_departemen')
                    ->label('Departemen')
                    ->options(function () {
                        return \App\Models\Departemen::all()
                            ->pluck('nama_departemen', 'id')
                            ->map(function ($name) {
                                return ucwords(strtolower($name));
                            })
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn(Builder $query): Builder => $query->whereHas('karyawan', function ($q) use ($data) {
                                $q->where('id_departemen', $data['value']);
                            })
                        );
                    }),

                Tables\Filters\SelectFilter::make('shield_role')
                    ->label('Role Shield')
                    ->options(function () {
                        return \Spatie\Permission\Models\Role::all()
                            ->pluck('name', 'name')
                            ->map(function ($name) {
                                return ucwords(str_replace('_', ' ', $name));
                            })
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn(Builder $query): Builder => $query->whereHas('roles', function ($q) use ($data) {
                                $q->where('name', $data['value']);
                            })
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                ...self::getExportActions(UserExport::class, 'Data User'),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    /**
     * Handle Shield role assignment after user creation/update
     */
    public static function handleShieldRoles($record, $data)
    {
        if (isset($data['shield_roles'])) {
            $record->syncRoles($data['shield_roles']);
        }
    }

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->with(['karyawan']);
    }
}
