<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.finance.resources.expense-category-resource.pages.create-expense-category' => 'App\\Filament\\Finance\\Resources\\ExpenseCategoryResource\\Pages\\CreateExpenseCategory',
    'app.filament.finance.resources.expense-category-resource.pages.edit-expense-category' => 'App\\Filament\\Finance\\Resources\\ExpenseCategoryResource\\Pages\\EditExpenseCategory',
    'app.filament.finance.resources.expense-category-resource.pages.list-expense-categories' => 'App\\Filament\\Finance\\Resources\\ExpenseCategoryResource\\Pages\\ListExpenseCategories',
    'app.filament.finance.resources.expense-category-resource.pages.view-expense-category' => 'App\\Filament\\Finance\\Resources\\ExpenseCategoryResource\\Pages\\ViewExpenseCategory',
    'app.filament.finance.resources.expense-request-resource.pages.create-expense-request' => 'App\\Filament\\Finance\\Resources\\ExpenseRequestResource\\Pages\\CreateExpenseRequest',
    'app.filament.finance.resources.expense-request-resource.pages.edit-expense-request' => 'App\\Filament\\Finance\\Resources\\ExpenseRequestResource\\Pages\\EditExpenseRequest',
    'app.filament.finance.resources.expense-request-resource.pages.list-expense-requests' => 'App\\Filament\\Finance\\Resources\\ExpenseRequestResource\\Pages\\ListExpenseRequests',
    'app.filament.finance.resources.expense-request-resource.pages.view-expense-request' => 'App\\Filament\\Finance\\Resources\\ExpenseRequestResource\\Pages\\ViewExpenseRequest',
    'app.filament.finance.resources.expense-request-resource.relation-managers.expense-request-items-relation-manager' => 'App\\Filament\\Finance\\Resources\\ExpenseRequestResource\\RelationManagers\\ExpenseRequestItemsRelationManager',
    'app.filament.finance.resources.petty-cash-fund-resource.pages.create-petty-cash-fund' => 'App\\Filament\\Finance\\Resources\\PettyCashFundResource\\Pages\\CreatePettyCashFund',
    'app.filament.finance.resources.petty-cash-fund-resource.pages.edit-petty-cash-fund' => 'App\\Filament\\Finance\\Resources\\PettyCashFundResource\\Pages\\EditPettyCashFund',
    'app.filament.finance.resources.petty-cash-fund-resource.pages.list-petty-cash-funds' => 'App\\Filament\\Finance\\Resources\\PettyCashFundResource\\Pages\\ListPettyCashFunds',
    'app.filament.finance.resources.petty-cash-fund-resource.pages.view-petty-cash-fund' => 'App\\Filament\\Finance\\Resources\\PettyCashFundResource\\Pages\\ViewPettyCashFund',
    'app.filament.finance.resources.petty-cash-fund-resource.relation-managers.petty-cash-transactions-relation-manager' => 'App\\Filament\\Finance\\Resources\\PettyCashFundResource\\RelationManagers\\PettyCashTransactionsRelationManager',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'filament.widgets.filament-info-widget' => 'Filament\\Widgets\\FilamentInfoWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Finance/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Finance\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Finance\\Resources\\ExpenseCategoryResource.php' => 'App\\Filament\\Finance\\Resources\\ExpenseCategoryResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Finance\\Resources\\ExpenseRequestResource.php' => 'App\\Filament\\Finance\\Resources\\ExpenseRequestResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Finance\\Resources\\PettyCashFundResource.php' => 'App\\Filament\\Finance\\Resources\\PettyCashFundResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Finance/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Finance\\Resources',
  ),
  'widgets' => 
  array (
    0 => 'Filament\\Widgets\\AccountWidget',
    1 => 'Filament\\Widgets\\FilamentInfoWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Finance/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Finance\\Widgets',
  ),
);