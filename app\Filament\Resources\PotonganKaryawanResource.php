<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PotonganKaryawanResource\Pages;
use App\Models\PotonganKaryawan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class PotonganKaryawanResource extends Resource
{
    protected static ?string $model = PotonganKaryawan::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationLabel = 'Potongan Karyawan';

    protected static ?string $modelLabel = 'Potongan Karyawan';

    protected static ?string $pluralModelLabel = 'Potongan Karyawan';

    protected static ?string $navigationGroup = 'Manajemen Karyawan';

    protected static ?int $navigationSort = 7;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Informasi Potongan')
                ->schema([
                    Forms\Components\Select::make('karyawan_id')
                        ->label('Karyawan')
                        ->relationship('karyawan', 'nama_lengkap')
                        ->searchable()
                        ->preload()
                        ->required()
                        ->getOptionLabelFromRecordUsing(fn($record) => "{$record->nama_lengkap}")
                        ->columnSpan(2),

                    Forms\Components\Select::make('jenis_potongan')
                        ->label('Jenis Potongan')
                        ->options([
                            'kasir' => 'Potongan Kasir',
                            'stok_opname' => 'Potongan Stok Opname',
                            'retur' => 'Potongan Retur',
                            'kasbon' => 'Potongan Kasbon',
                        ])
                        ->required()
                        ->searchable()
                        ->preload()
                        ->columnSpan(1),

                    Forms\Components\DatePicker::make('bulan_potongan')
                        ->label('Tanggal Potongan')
                        ->required()
                        ->native(false)
                        ->displayFormat('d M Y')
                        ->helperText('Pilih tanggal untuk periode potongan')
                        ->columnSpan(1),
                ])
                ->columns(2),

            Forms\Components\Section::make('Detail Potongan')
                ->schema([
                    Forms\Components\TextInput::make('nominal')
                        ->label('Nominal Potongan')
                        ->required()
                        ->numeric()
                        ->prefix('Rp')
                        ->minValue(0)
                        ->placeholder('0')
                        ->formatStateUsing(fn($state) => $state ? number_format($state, 0, ',', '.') : '')
                        ->dehydrateStateUsing(fn($state) => (float) str_replace(['.', ','], ['', '.'], $state))
                        ->columnSpan(1),

                    Forms\Components\Textarea::make('keterangan')
                        ->label('Keterangan')
                        ->placeholder('Keterangan tambahan (opsional)')
                        ->rows(4)
                        ->columnSpan(1),
                ])
                ->columns(1),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->label('Karyawan')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('karyawan.jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->placeholder('-')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('jenis_potongan')
                    ->label('Jenis Potongan')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'kasir' => 'warning',
                        'stok_opname' => 'info',
                        'retur' => 'danger',
                        'kasbon' => 'secondary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'kasir' => 'Potongan Kasir',
                        'stok_opname' => 'Potongan Stok Opname',
                        'retur' => 'Potongan Retur',
                        'kasbon' => 'Potongan Kasbon',
                        default => $state,
                    })
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('nominal')
                    ->label('Nominal')
                    ->money('IDR')
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('bulan_potongan')
                    ->label('Tanggal Potongan')
                    ->date('d M Y')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->placeholder('-')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->placeholder('-')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_potongan')
                    ->label('Jenis Potongan')
                    ->options([
                        'kasir' => 'Potongan Kasir',
                        'stok_opname' => 'Potongan Stok Opname',
                        'retur' => 'Potongan Retur',
                        'kasbon' => 'Potongan Kasbon',
                    ]),

                Tables\Filters\Filter::make('tanggal_potongan')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal')
                            ->native(false),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal')
                            ->native(false),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn($query, $date) => $query->whereDate('bulan_potongan', '>=', $date)
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn($query, $date) => $query->whereDate('bulan_potongan', '<=', $date)
                            );
                    }),

                Tables\Filters\SelectFilter::make('karyawan')
                    ->relationship('karyawan', 'nama_lengkap')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->icon('heroicon-o-eye'),

                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil-square'),

                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Belum ada data potongan karyawan')
            ->emptyStateDescription('Tambahkan potongan karyawan dengan mengklik tombol "Tambah Potongan Karyawan".')
            ->emptyStateIcon('heroicon-o-currency-dollar');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPotonganKaryawans::route('/'),
            'create' => Pages\CreatePotonganKaryawan::route('/create'),
            'view' => Pages\ViewPotonganKaryawan::route('/{record}'),
            'edit' => Pages\EditPotonganKaryawan::route('/{record}/edit'),
        ];
    }
}
