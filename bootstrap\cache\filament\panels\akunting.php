<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.akunting.resources.category-resource.pages.create-category' => 'App\\Filament\\Akunting\\Resources\\CategoryResource\\Pages\\CreateCategory',
    'app.filament.akunting.resources.category-resource.pages.edit-category' => 'App\\Filament\\Akunting\\Resources\\CategoryResource\\Pages\\EditCategory',
    'app.filament.akunting.resources.category-resource.pages.list-categories' => 'App\\Filament\\Akunting\\Resources\\CategoryResource\\Pages\\ListCategories',
    'app.filament.akunting.resources.daily-transaction-resource.pages.create-daily-transaction' => 'App\\Filament\\Akunting\\Resources\\DailyTransactionResource\\Pages\\CreateDailyTransaction',
    'app.filament.akunting.resources.daily-transaction-resource.pages.edit-daily-transaction' => 'App\\Filament\\Akunting\\Resources\\DailyTransactionResource\\Pages\\EditDailyTransaction',
    'app.filament.akunting.resources.daily-transaction-resource.pages.list-daily-transactions' => 'App\\Filament\\Akunting\\Resources\\DailyTransactionResource\\Pages\\ListDailyTransactions',
    'app.filament.akunting.resources.daily-transaction-resource.widgets.transaction-stats-widget' => 'App\\Filament\\Akunting\\Resources\\DailyTransactionResource\\Widgets\\TransactionStatsWidget',
    'app.filament.akunting.resources.outlet-resource.pages.create-outlet' => 'App\\Filament\\Akunting\\Resources\\OutletResource\\Pages\\CreateOutlet',
    'app.filament.akunting.resources.outlet-resource.pages.edit-outlet' => 'App\\Filament\\Akunting\\Resources\\OutletResource\\Pages\\EditOutlet',
    'app.filament.akunting.resources.outlet-resource.pages.list-outlets' => 'App\\Filament\\Akunting\\Resources\\OutletResource\\Pages\\ListOutlets',
    'app.filament.akunting.resources.transaction-category-resource.pages.create-transaction-category' => 'App\\Filament\\Akunting\\Resources\\TransactionCategoryResource\\Pages\\CreateTransactionCategory',
    'app.filament.akunting.resources.transaction-category-resource.pages.edit-transaction-category' => 'App\\Filament\\Akunting\\Resources\\TransactionCategoryResource\\Pages\\EditTransactionCategory',
    'app.filament.akunting.resources.transaction-category-resource.pages.list-transaction-categories' => 'App\\Filament\\Akunting\\Resources\\TransactionCategoryResource\\Pages\\ListTransactionCategories',
    'app.filament.akunting.resources.transaction-subcategory-resource.pages.create-transaction-subcategory' => 'App\\Filament\\Akunting\\Resources\\TransactionSubcategoryResource\\Pages\\CreateTransactionSubcategory',
    'app.filament.akunting.resources.transaction-subcategory-resource.pages.edit-transaction-subcategory' => 'App\\Filament\\Akunting\\Resources\\TransactionSubcategoryResource\\Pages\\EditTransactionSubcategory',
    'app.filament.akunting.resources.transaction-subcategory-resource.pages.list-transaction-subcategories' => 'App\\Filament\\Akunting\\Resources\\TransactionSubcategoryResource\\Pages\\ListTransactionSubcategories',
    'app.filament.akunting.pages.combined-monthly-report' => 'App\\Filament\\Akunting\\Pages\\CombinedMonthlyReport',
    'app.filament.akunting.pages.fn-b-monthly-report' => 'App\\Filament\\Akunting\\Pages\\FnBMonthlyReport',
    'app.filament.akunting.pages.monthly-outlet-report' => 'App\\Filament\\Akunting\\Pages\\MonthlyOutletReport',
    'app.filament.akunting.pages.v-o-o-monthly-report' => 'App\\Filament\\Akunting\\Pages\\VOOMonthlyReport',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'app.filament.akunting.widgets.category-comparison-chart' => 'App\\Filament\\Akunting\\Widgets\\CategoryComparisonChart',
    'app.filament.akunting.widgets.monthly-trend-chart' => 'App\\Filament\\Akunting\\Widgets\\MonthlyTrendChart',
    'app.filament.akunting.widgets.p-l-overview-widget' => 'App\\Filament\\Akunting\\Widgets\\PLOverviewWidget',
    'app.filament.akunting.widgets.recent-transactions-widget' => 'App\\Filament\\Akunting\\Widgets\\RecentTransactionsWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Pages\\CombinedMonthlyReport.php' => 'App\\Filament\\Akunting\\Pages\\CombinedMonthlyReport',
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Pages\\FnBMonthlyReport.php' => 'App\\Filament\\Akunting\\Pages\\FnBMonthlyReport',
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Pages\\MonthlyOutletReport.php' => 'App\\Filament\\Akunting\\Pages\\MonthlyOutletReport',
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Pages\\VOOMonthlyReport.php' => 'App\\Filament\\Akunting\\Pages\\VOOMonthlyReport',
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Akunting/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Akunting\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Resources\\DailyTransactionResource.php' => 'App\\Filament\\Akunting\\Resources\\DailyTransactionResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Resources\\OutletResource.php' => 'App\\Filament\\Akunting\\Resources\\OutletResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Resources\\TransactionCategoryResource.php' => 'App\\Filament\\Akunting\\Resources\\TransactionCategoryResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Resources\\TransactionSubcategoryResource.php' => 'App\\Filament\\Akunting\\Resources\\TransactionSubcategoryResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Akunting/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Akunting\\Resources',
  ),
  'widgets' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Widgets\\CategoryComparisonChart.php' => 'App\\Filament\\Akunting\\Widgets\\CategoryComparisonChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Widgets\\MonthlyTrendChart.php' => 'App\\Filament\\Akunting\\Widgets\\MonthlyTrendChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Widgets\\PLOverviewWidget.php' => 'App\\Filament\\Akunting\\Widgets\\PLOverviewWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Akunting\\Widgets\\RecentTransactionsWidget.php' => 'App\\Filament\\Akunting\\Widgets\\RecentTransactionsWidget',
    0 => 'App\\Filament\\Akunting\\Widgets\\PLOverviewWidget',
    1 => 'App\\Filament\\Akunting\\Widgets\\MonthlyTrendChart',
    2 => 'App\\Filament\\Akunting\\Widgets\\CategoryComparisonChart',
    3 => 'App\\Filament\\Akunting\\Widgets\\RecentTransactionsWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Akunting/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Akunting\\Widgets',
  ),
);