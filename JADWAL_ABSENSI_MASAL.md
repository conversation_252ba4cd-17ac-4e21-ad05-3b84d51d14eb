# Fitur Jadwal & Abs<PERSON>i Masal

## Overview
Fitur Jadwal & Absensi Masal memungkinkan admin untuk membuat jadwal kerja dan record absensi secara massal untuk periode tertentu dengan shift yang sama.

## Fitur Utama

### 1. **Mass Assignment Jadwal + Kehadiran**
- Input tanggal mulai dan tanggal selesai
- <PERSON><PERSON><PERSON> shift yang tersedia
- Auto-populate data absensi sesuai dengan shift
- Tidak ada field foto (sesuai permintaan)

### 2. **Form Input**
- **Nama Jadwal**: Identifikasi untuk batch jadwal
- **Periode**: Tanggal mulai dan selesai
- **Shift**: <PERSON><PERSON><PERSON> shift yang aktif
- **Entitas**: Lokasi kerja (opsional)
- **Karyawan**: Multi-select karyawan
- **Estimasi**: Preview total record yang akan dibuat

### 3. **Auto-Population Data**
- **Jadwal Kerja**: Otomatis dibuat untuk setiap hari dalam periode
- **Absensi**: Record absensi dengan waktu sesuai shift
- **Split Shift**: Support untuk shift dengan 2 periode
- **Status**: Default "hadir" untuk semua record absensi

## Database Structure

### Tabel `jadwal_absensi_masal`
```sql
- id (primary key)
- nama_jadwal (string)
- tanggal_mulai (date)
- tanggal_selesai (date)
- shift_id (foreign key)
- entitas_id (foreign key, nullable)
- created_by (foreign key)
- keterangan (text, nullable)
- generated_at (timestamp, nullable)
- status (enum: draft, completed, cancelled)
- timestamps
```

### Tabel `jadwal_absensi_masal_karyawan` (Pivot)
```sql
- id (primary key)
- jadwal_absensi_masal_id (foreign key)
- karyawan_id (foreign key)
- timestamps
```

### Update Tabel `absensi`
```sql
+ entitas_id (foreign key, nullable) - untuk mass assignment
```

## Workflow

### 1. **Pembuatan Jadwal Masal**
1. Admin mengisi form jadwal absensi masal
2. Pilih periode (tanggal mulai - selesai)
3. Pilih shift yang akan digunakan
4. Pilih entitas (opsional)
5. Pilih karyawan yang akan dijadwalkan
6. Review estimasi total record
7. Simpan sebagai draft

### 2. **Generate Jadwal & Absensi**
1. Klik tombol "Generate" pada record draft
2. Sistem akan membuat:
   - Record jadwal kerja untuk setiap hari dalam periode
   - Record absensi dengan waktu sesuai shift
   - Support split shift (2 record absensi per hari)
3. Status berubah menjadi "completed"
4. Tidak bisa diedit setelah di-generate

### 3. **Data yang Dibuat**

#### Jadwal Kerja (`jadwal_kerja`)
```php
[
    'karyawan_id' => $karyawanId,
    'shift_id' => $shift->id,
    'entitas_id' => $entitasId,
    'supervisor_id' => $createdBy,
    'tanggal_jadwal' => $dateString,
    'waktu_masuk' => $shift->waktu_mulai,
    'waktu_keluar' => $shift->waktu_selesai,
    'is_approved' => true,
]
```

#### Absensi (`absensi`)
```php
[
    'karyawan_id' => $karyawanId,
    'jadwal_id' => $schedule->id,
    'entitas_id' => $entitasId,
    'tanggal_absensi' => $dateString,
    'waktu_masuk' => $shift->waktu_mulai,
    'waktu_keluar' => $shift->waktu_selesai,
    'status' => 'hadir',
    'periode' => 1, // atau 2 untuk split shift
    'keterangan' => 'Auto-generated dari jadwal absensi masal',
]
```

## Keunggulan

### 1. **Efisiensi**
- Buat ratusan record jadwal dan absensi dalam sekali klik
- Tidak perlu input manual satu per satu
- Estimasi real-time jumlah record yang akan dibuat

### 2. **Fleksibilitas**
- Support regular shift dan split shift
- Bisa pilih entitas spesifik atau semua entitas
- Bisa pilih karyawan tertentu saja

### 3. **Keamanan**
- Tidak bisa edit setelah di-generate
- Validasi data sebelum generate
- Rollback otomatis jika ada error

### 4. **User Experience**
- Form yang intuitif dengan preview
- Notifikasi hasil generate
- Status tracking yang jelas

## Penggunaan

### 1. **Akses Menu**
- Navigasi: Manajemen Jadwal > Jadwal & Absensi Masal

### 2. **Membuat Jadwal Baru**
```
1. Klik "Buat Jadwal & Absensi Masal"
2. Isi nama jadwal (contoh: "Jadwal Januari 2025 - Shift Pagi")
3. Pilih tanggal mulai dan selesai
4. Pilih shift
5. Pilih entitas (opsional)
6. Pilih karyawan (multi-select)
7. Review estimasi
8. Simpan
```

### 3. **Generate Data**
```
1. Buka detail jadwal yang sudah dibuat
2. Klik tombol "Generate"
3. Konfirmasi di modal
4. Tunggu proses selesai
5. Lihat notifikasi hasil
```

## Contoh Skenario

### Skenario 1: Jadwal Bulanan Shift Pagi
- **Periode**: 1 Januari - 31 Januari 2025
- **Shift**: Pagi (08:00 - 17:00)
- **Karyawan**: 10 orang
- **Hasil**: 310 jadwal + 310 absensi = 620 record

### Skenario 2: Jadwal Mingguan Split Shift
- **Periode**: 1 Januari - 7 Januari 2025
- **Shift**: Split (08:00-12:00, 13:00-17:00)
- **Karyawan**: 5 orang
- **Hasil**: 35 jadwal + 70 absensi = 105 record

## Validasi & Error Handling

### 1. **Validasi Form**
- Tanggal selesai harus >= tanggal mulai
- Minimal 1 karyawan harus dipilih
- Shift harus aktif

### 2. **Error Handling**
- Duplicate detection untuk jadwal existing
- Rollback otomatis jika ada error
- Pesan error yang informatif

### 3. **Performance**
- Batch processing untuk data besar
- Progress tracking untuk proses panjang
- Memory optimization

## Migration & Setup

### 1. **Jalankan Migration**
```bash
php artisan migrate
```

### 2. **Jalankan Seeder (Opsional)**
```bash
php artisan db:seed --class=JadwalAbsensiMasalSeeder
```

### 3. **Clear Cache**
```bash
php artisan route:cache
php artisan config:cache
```

## Catatan Penting

1. **Backup Data**: Selalu backup sebelum generate data massal
2. **Testing**: Test dengan data kecil dulu sebelum production
3. **Performance**: Monitor performance untuk data besar (>1000 record)
4. **Permissions**: Pastikan user memiliki permission yang sesuai
5. **Storage**: Pastikan storage cukup untuk data yang akan dibuat

## Troubleshooting

### 1. **Generate Gagal**
- Cek koneksi database
- Cek permission user
- Cek validasi data

### 2. **Performance Lambat**
- Kurangi jumlah karyawan per batch
- Kurangi periode tanggal
- Cek index database

### 3. **Memory Error**
- Increase PHP memory limit
- Gunakan batch processing
- Optimize query
