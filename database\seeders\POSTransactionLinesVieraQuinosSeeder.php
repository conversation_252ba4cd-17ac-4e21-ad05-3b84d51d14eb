<?php

namespace Database\Seeders;

use App\Models\PosTransactionItem;
use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use League\Csv\Reader;

class POSTransactionLinesVieraQuinosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->call([
            POSTransactionsVieraQuinosSeeder::class,
        ]);

        $csv = Reader::createFromPath(database_path('seeders/data/pos_transaction_lines_viera.csv'), 'r');
        $csv->setHeaderOffset(0);

        $count = $csv->count();
        $bar = $this->command->getOutput()->createProgressBar($count);
        $bar->start();

        $product_umum = Product::where('nama', 'UMUM')->first();
        if (!$product_umum) {
            $product_umum = Product::create([
                'nama' => 'UMUM',
                'alamat' => 'UMUM',
                'tipe' => 'UMUM',
                'is_active' => true,
            ]);
        }

        try {
            DB::transaction(function () use ($csv, $bar, $product_umum) {
                foreach ($csv->getRecords() as $data) {
                    if (!$data['product_name'] || strtoupper($data['product_name']) == 'UMUM') {
                        $data['product_id'] = $product_umum->id;
                    } else {
                        $data['product_id'] = (Product::where('name', $data['product_name'])->first() ?? $product_umum)->id;
                    }

                    if (!PosTransactionItem::where('pos_transaction_id', $data['pos_transaction_id'])->where('product_id', $data['product_id'])->first()) {
                        unset($data['product_name']);
                        PosTransactionItem::create($data);
                        $bar->advance();
                    }
                }
            });

            $bar->finish();
            $this->command->newLine();
            $this->command->info("✅ Selesai insert {$count} data PosTransactionLines");
        } catch (\Throwable $e) {
            $bar->clear();
            $this->command->error("❌ Gagal insert data: " . $e->getMessage());
        }
    }
}
