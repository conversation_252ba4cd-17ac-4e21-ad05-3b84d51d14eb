<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('shift', function (Blueprint $table) {
            // Add is_beda_hari2 field for split shift period 2
            if (!Schema::hasColumn('shift', 'is_beda_hari2')) {
                $table->boolean('is_beda_hari2')->default(false)->after('is_beda_hari')
                    ->comment('True if split shift period 2 end time is on the next day');
            }
            
            // Update index to include the new field
            $table->dropIndex('idx_shift_beda_hari_active');
            $table->index(['is_beda_hari', 'is_beda_hari2', 'is_active'], 'idx_shift_beda_hari_active');
        });
    }

    public function down(): void
    {
        Schema::table('shift', function (Blueprint $table) {
            $table->dropIndex('idx_shift_beda_hari_active');
            
            if (Schema::hasColumn('shift', 'is_beda_hari2')) {
                $table->dropColumn('is_beda_hari2');
            }
            
            // Restore original index
            $table->index(['is_beda_hari', 'is_active'], 'idx_shift_beda_hari_active');
        });
    }
};
