<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.mitra.resources.konsinyasi-receipt-resource.pages.create-konsinyasi-receipt' => 'App\\Filament\\Mitra\\Resources\\KonsinyasiReceiptResource\\Pages\\CreateKonsinyasiReceipt',
    'app.filament.mitra.resources.konsinyasi-receipt-resource.pages.edit-konsinyasi-receipt' => 'App\\Filament\\Mitra\\Resources\\KonsinyasiReceiptResource\\Pages\\EditKonsinyasiReceipt',
    'app.filament.mitra.resources.konsinyasi-receipt-resource.pages.list-konsinyasi-receipts' => 'App\\Filament\\Mitra\\Resources\\KonsinyasiReceiptResource\\Pages\\ListKonsinyasiReceipts',
    'app.filament.mitra.resources.mitra-resource.pages.create-mitra' => 'App\\Filament\\Mitra\\Resources\\MitraResource\\Pages\\CreateMitra',
    'app.filament.mitra.resources.mitra-resource.pages.edit-mitra' => 'App\\Filament\\Mitra\\Resources\\MitraResource\\Pages\\EditMitra',
    'app.filament.mitra.resources.mitra-resource.pages.list-mitras' => 'App\\Filament\\Mitra\\Resources\\MitraResource\\Pages\\ListMitras',
    'app.filament.mitra.resources.mitra-resource.relation-managers.konsinyasi-receipts-relation-manager' => 'App\\Filament\\Mitra\\Resources\\MitraResource\\RelationManagers\\KonsinyasiReceiptsRelationManager',
    'app.filament.mitra.resources.mitra-resource.relation-managers.products-relation-manager' => 'App\\Filament\\Mitra\\Resources\\MitraResource\\RelationManagers\\ProductsRelationManager',
    'app.filament.mitra.resources.mitra-resource.relation-managers.sales-history-relation-manager' => 'App\\Filament\\Mitra\\Resources\\MitraResource\\RelationManagers\\SalesHistoryRelationManager',
    'app.filament.mitra.resources.product-placement-request-resource.pages.create-product-placement-request' => 'App\\Filament\\Mitra\\Resources\\ProductPlacementRequestResource\\Pages\\CreateProductPlacementRequest',
    'app.filament.mitra.resources.product-placement-request-resource.pages.edit-product-placement-request' => 'App\\Filament\\Mitra\\Resources\\ProductPlacementRequestResource\\Pages\\EditProductPlacementRequest',
    'app.filament.mitra.resources.product-placement-request-resource.pages.list-product-placement-requests' => 'App\\Filament\\Mitra\\Resources\\ProductPlacementRequestResource\\Pages\\ListProductPlacementRequests',
    'app.filament.mitra.resources.produk-resource.pages.create-produk' => 'App\\Filament\\Mitra\\Resources\\ProdukResource\\Pages\\CreateProduk',
    'app.filament.mitra.resources.produk-resource.pages.edit-produk' => 'App\\Filament\\Mitra\\Resources\\ProdukResource\\Pages\\EditProduk',
    'app.filament.mitra.resources.produk-resource.pages.list-produks' => 'App\\Filament\\Mitra\\Resources\\ProdukResource\\Pages\\ListProduks',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'app.filament.mitra.widgets.konsinyasi-stats-widget' => 'App\\Filament\\Mitra\\Widgets\\KonsinyasiStatsWidget',
    'app.filament.mitra.widgets.mitra-chart-widget' => 'App\\Filament\\Mitra\\Widgets\\MitraChartWidget',
    'app.filament.mitra.widgets.mitra-stats-widget' => 'App\\Filament\\Mitra\\Widgets\\MitraStatsWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Mitra/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Mitra\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Mitra\\Resources\\KonsinyasiReceiptResource.php' => 'App\\Filament\\Mitra\\Resources\\KonsinyasiReceiptResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Mitra\\Resources\\MitraResource.php' => 'App\\Filament\\Mitra\\Resources\\MitraResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Mitra\\Resources\\ProductPlacementRequestResource.php' => 'App\\Filament\\Mitra\\Resources\\ProductPlacementRequestResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Mitra\\Resources\\ProdukResource.php' => 'App\\Filament\\Mitra\\Resources\\ProdukResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Mitra/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Mitra\\Resources',
  ),
  'widgets' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Mitra\\Widgets\\KonsinyasiStatsWidget.php' => 'App\\Filament\\Mitra\\Widgets\\KonsinyasiStatsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Mitra\\Widgets\\MitraChartWidget.php' => 'App\\Filament\\Mitra\\Widgets\\MitraChartWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Mitra\\Widgets\\MitraStatsWidget.php' => 'App\\Filament\\Mitra\\Widgets\\MitraStatsWidget',
    0 => 'App\\Filament\\Mitra\\Widgets\\MitraStatsWidget',
    1 => 'App\\Filament\\Mitra\\Widgets\\KonsinyasiStatsWidget',
    2 => 'App\\Filament\\Mitra\\Widgets\\MitraChartWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Mitra/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Mitra\\Widgets',
  ),
);