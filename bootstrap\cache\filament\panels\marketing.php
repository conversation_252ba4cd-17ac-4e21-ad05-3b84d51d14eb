<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.marketing.resources.category-resource.pages.create-category' => 'App\\Filament\\Marketing\\Resources\\CategoryResource\\Pages\\CreateCategory',
    'app.filament.marketing.resources.category-resource.pages.edit-category' => 'App\\Filament\\Marketing\\Resources\\CategoryResource\\Pages\\EditCategory',
    'app.filament.marketing.resources.category-resource.pages.list-categories' => 'App\\Filament\\Marketing\\Resources\\CategoryResource\\Pages\\ListCategories',
    'app.filament.marketing.resources.city-resource.pages.create-city' => 'App\\Filament\\Marketing\\Resources\\CityResource\\Pages\\CreateCity',
    'app.filament.marketing.resources.city-resource.pages.edit-city' => 'App\\Filament\\Marketing\\Resources\\CityResource\\Pages\\EditCity',
    'app.filament.marketing.resources.city-resource.pages.list-cities' => 'App\\Filament\\Marketing\\Resources\\CityResource\\Pages\\ListCities',
    'app.filament.marketing.resources.customer-feedback-resource.pages.create-customer-feedback' => 'App\\Filament\\Marketing\\Resources\\CustomerFeedbackResource\\Pages\\CreateCustomerFeedback',
    'app.filament.marketing.resources.customer-feedback-resource.pages.edit-customer-feedback' => 'App\\Filament\\Marketing\\Resources\\CustomerFeedbackResource\\Pages\\EditCustomerFeedback',
    'app.filament.marketing.resources.customer-feedback-resource.pages.list-customer-feedback' => 'App\\Filament\\Marketing\\Resources\\CustomerFeedbackResource\\Pages\\ListCustomerFeedback',
    'app.filament.marketing.resources.customer-resource.pages.create-customer' => 'App\\Filament\\Marketing\\Resources\\CustomerResource\\Pages\\CreateCustomer',
    'app.filament.marketing.resources.customer-resource.pages.edit-customer' => 'App\\Filament\\Marketing\\Resources\\CustomerResource\\Pages\\EditCustomer',
    'app.filament.marketing.resources.customer-resource.pages.list-customers' => 'App\\Filament\\Marketing\\Resources\\CustomerResource\\Pages\\ListCustomers',
    'app.filament.marketing.resources.customer-resource.relation-managers.loyalty-transactions-relation-manager' => 'App\\Filament\\Marketing\\Resources\\CustomerResource\\RelationManagers\\LoyaltyTransactionsRelationManager',
    'app.filament.marketing.resources.customer-resource.relation-managers.pos-transactions-relation-manager' => 'App\\Filament\\Marketing\\Resources\\CustomerResource\\RelationManagers\\PosTransactionsRelationManager',
    'app.filament.marketing.resources.district-resource.pages.create-district' => 'App\\Filament\\Marketing\\Resources\\DistrictResource\\Pages\\CreateDistrict',
    'app.filament.marketing.resources.district-resource.pages.edit-district' => 'App\\Filament\\Marketing\\Resources\\DistrictResource\\Pages\\EditDistrict',
    'app.filament.marketing.resources.district-resource.pages.list-districts' => 'App\\Filament\\Marketing\\Resources\\DistrictResource\\Pages\\ListDistricts',
    'app.filament.marketing.resources.loyalty-program-resource.pages.create-loyalty-program' => 'App\\Filament\\Marketing\\Resources\\LoyaltyProgramResource\\Pages\\CreateLoyaltyProgram',
    'app.filament.marketing.resources.loyalty-program-resource.pages.edit-loyalty-program' => 'App\\Filament\\Marketing\\Resources\\LoyaltyProgramResource\\Pages\\EditLoyaltyProgram',
    'app.filament.marketing.resources.loyalty-program-resource.pages.list-loyalty-programs' => 'App\\Filament\\Marketing\\Resources\\LoyaltyProgramResource\\Pages\\ListLoyaltyPrograms',
    'app.filament.marketing.resources.loyalty-transaction-resource.pages.create-loyalty-transaction' => 'App\\Filament\\Marketing\\Resources\\LoyaltyTransactionResource\\Pages\\CreateLoyaltyTransaction',
    'app.filament.marketing.resources.loyalty-transaction-resource.pages.edit-loyalty-transaction' => 'App\\Filament\\Marketing\\Resources\\LoyaltyTransactionResource\\Pages\\EditLoyaltyTransaction',
    'app.filament.marketing.resources.loyalty-transaction-resource.pages.list-loyalty-transactions' => 'App\\Filament\\Marketing\\Resources\\LoyaltyTransactionResource\\Pages\\ListLoyaltyTransactions',
    'app.filament.marketing.resources.pos-transaction-resource.pages.create-pos-transaction' => 'App\\Filament\\Marketing\\Resources\\PosTransactionResource\\Pages\\CreatePosTransaction',
    'app.filament.marketing.resources.pos-transaction-resource.pages.edit-pos-transaction' => 'App\\Filament\\Marketing\\Resources\\PosTransactionResource\\Pages\\EditPosTransaction',
    'app.filament.marketing.resources.pos-transaction-resource.pages.list-pos-transactions' => 'App\\Filament\\Marketing\\Resources\\PosTransactionResource\\Pages\\ListPosTransactions',
    'app.filament.marketing.resources.pos-transaction-resource.pages.view-pos-transaction' => 'App\\Filament\\Marketing\\Resources\\PosTransactionResource\\Pages\\ViewPosTransaction',
    'app.filament.marketing.resources.product-resource.pages.create-product' => 'App\\Filament\\Marketing\\Resources\\ProductResource\\Pages\\CreateProduct',
    'app.filament.marketing.resources.product-resource.pages.edit-product' => 'App\\Filament\\Marketing\\Resources\\ProductResource\\Pages\\EditProduct',
    'app.filament.marketing.resources.product-resource.pages.list-products' => 'App\\Filament\\Marketing\\Resources\\ProductResource\\Pages\\ListProducts',
    'app.filament.marketing.resources.province-resource.pages.create-province' => 'App\\Filament\\Marketing\\Resources\\ProvinceResource\\Pages\\CreateProvince',
    'app.filament.marketing.resources.province-resource.pages.edit-province' => 'App\\Filament\\Marketing\\Resources\\ProvinceResource\\Pages\\EditProvince',
    'app.filament.marketing.resources.province-resource.pages.list-provinces' => 'App\\Filament\\Marketing\\Resources\\ProvinceResource\\Pages\\ListProvinces',
    'app.filament.marketing.resources.village-resource.pages.create-village' => 'App\\Filament\\Marketing\\Resources\\VillageResource\\Pages\\CreateVillage',
    'app.filament.marketing.resources.village-resource.pages.edit-village' => 'App\\Filament\\Marketing\\Resources\\VillageResource\\Pages\\EditVillage',
    'app.filament.marketing.resources.village-resource.pages.list-villages' => 'App\\Filament\\Marketing\\Resources\\VillageResource\\Pages\\ListVillages',
    'app.filament.marketing.pages.dashboard' => 'App\\Filament\\Marketing\\Pages\\Dashboard',
    'app.filament.marketing.pages.pos-interface' => 'App\\Filament\\Marketing\\Pages\\PosInterface',
    'app.filament.marketing.pages.sales-report-page' => 'App\\Filament\\Marketing\\Pages\\SalesReportPage',
    'app.filament.marketing.widgets.customer-segment-chart' => 'App\\Filament\\Marketing\\Widgets\\CustomerSegmentChart',
    'app.filament.marketing.widgets.top-products-widget' => 'App\\Filament\\Marketing\\Widgets\\TopProductsWidget',
    'app.filament.marketing.widgets.total-customers-widget' => 'App\\Filament\\Marketing\\Widgets\\TotalCustomersWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Pages\\Dashboard.php' => 'App\\Filament\\Marketing\\Pages\\Dashboard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Pages\\PosInterface.php' => 'App\\Filament\\Marketing\\Pages\\PosInterface',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Pages\\SalesReportPage.php' => 'App\\Filament\\Marketing\\Pages\\SalesReportPage',
    0 => 'App\\Filament\\Marketing\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Marketing/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Marketing\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\CategoryResource.php' => 'App\\Filament\\Marketing\\Resources\\CategoryResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\CityResource.php' => 'App\\Filament\\Marketing\\Resources\\CityResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\CustomerFeedbackResource.php' => 'App\\Filament\\Marketing\\Resources\\CustomerFeedbackResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\CustomerResource.php' => 'App\\Filament\\Marketing\\Resources\\CustomerResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\DistrictResource.php' => 'App\\Filament\\Marketing\\Resources\\DistrictResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\LoyaltyProgramResource.php' => 'App\\Filament\\Marketing\\Resources\\LoyaltyProgramResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\LoyaltyTransactionResource.php' => 'App\\Filament\\Marketing\\Resources\\LoyaltyTransactionResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\PosTransactionResource.php' => 'App\\Filament\\Marketing\\Resources\\PosTransactionResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\ProductResource.php' => 'App\\Filament\\Marketing\\Resources\\ProductResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\ProvinceResource.php' => 'App\\Filament\\Marketing\\Resources\\ProvinceResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Resources\\VillageResource.php' => 'App\\Filament\\Marketing\\Resources\\VillageResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Marketing/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Marketing\\Resources',
  ),
  'widgets' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Widgets\\CustomerSegmentChart.php' => 'App\\Filament\\Marketing\\Widgets\\CustomerSegmentChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Widgets\\TopProductsWidget.php' => 'App\\Filament\\Marketing\\Widgets\\TopProductsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Marketing\\Widgets\\TotalCustomersWidget.php' => 'App\\Filament\\Marketing\\Widgets\\TotalCustomersWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Marketing/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Marketing\\Widgets',
  ),
);