<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LemburResource\Pages;
use App\Models\Lembur;
use App\Models\Karyawan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Placeholder;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Collection;
use Carbon\Carbon;
use Filament\Support\Enums\MaxWidth;
use EightyNine\Approvals\Tables\Actions\ApprovalActions;
use App\Services\PermissionService;

class LemburResource extends Resource
{
    protected static ?string $model = Lembur::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $navigationGroup = 'Jadwal & Absensi';

    protected static ?string $navigationLabel = 'Data Lembur';

    protected static ?string $modelLabel = 'Lembur';

    protected static ?string $pluralModelLabel = 'Data Lembur';

    protected static ?int $navigationSort = 7;

    public static function canAccess(): bool
    {
        $user = auth()->user();
        if (!$user) return false;

        // Admin and supervisor can always access
        if ($user->role === 'admin' || $user->role === 'supervisor') {
            return true;
        }

        // Shield roles that can access
        if ($user->hasAnyRole(['super_admin', 'manager_hrd', 'direktur', 'manager_produksi'])) {
            return true;
        }

        // Manager and keptok can access
        if (in_array($user->role, ['manager', 'keptok'])) {
            return true;
        }

        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Lembur')
                    ->schema([
                        // approval progress column

                        Grid::make(2)
                            ->schema([
                                Select::make('karyawan_id')
                                    ->label('Karyawan')
                                    ->relationship(
                                        'karyawan',
                                        'nama_lengkap',
                                        modifyQueryUsing: function ($query) {
                                            // Filter berdasarkan permission policy
                                            $accessibleIds = \App\Services\PermissionService::getAccessibleKaryawanIds('manage_overtime');
                                            return $query->whereIn('id', $accessibleIds);
                                        }
                                    )
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->getOptionLabelFromRecordUsing(fn($record) => "{$record->nama_lengkap} - {$record->nip}")
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        if ($state) {
                                            // Hitung total jam lembur bulan ini untuk karyawan yang dipilih
                                            $currentMonth = now()->month;
                                            $currentYear = now()->year;

                                            $totalJamBulanIni = Lembur::where('karyawan_id', $state)
                                                ->whereMonth('tanggal', $currentMonth)
                                                ->whereYear('tanggal', $currentYear)
                                                ->sum('jumlah_jam');

                                            // Set helper text dengan informasi jam lembur
                                            $set('karyawan_info', "Total jam lembur bulan ini: " . number_format($totalJamBulanIni, 1) . " jam");
                                        }
                                    })
                                    ->helperText(fn($get) => $get('karyawan_info') ?? 'Pilih karyawan untuk melihat informasi lembur'),

                                DatePicker::make('tanggal')
                                    ->label('Tanggal Lembur')
                                    ->required()
                                    ->default(now())
                                    ->maxDate(now())
                                    ->rule(function () {
                                        return function ($attribute, $value, $fail) {
                                            // Validasi tidak boleh weekend (opsional)
                                            $date = Carbon::parse($value);
                                            if ($date->isWeekend()) {
                                                $fail('Tanggal lembur tidak boleh di hari weekend.');
                                            }

                                            // Validasi tidak boleh lebih dari 30 hari yang lalu
                                            if ($date->lt(now()->subDays(30))) {
                                                $fail('Tanggal lembur tidak boleh lebih dari 30 hari yang lalu.');
                                            }
                                        };
                                    }),
                            ]),

                        Grid::make(1)
                            ->schema([
                                // Hidden field untuk menyimpan info karyawan
                                Forms\Components\Hidden::make('karyawan_info'),

                                Select::make('jenis_lembur_id')
                                    ->label('Jenis Lembur')
                                    ->relationship('jenisLembur', 'nama_jenis')
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->helperText('Pilih jenis lembur yang sesuai')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        if ($state) {
                                            $jenisLembur = \App\Models\JenisLembur::find($state);

                                            // Auto-fill jam untuk lembur HK (per hari) = 8 jam
                                            if ($jenisLembur && $jenisLembur->tipe_perhitungan === 'per_hari') {
                                                $set('jumlah_jam', 8);
                                            } elseif ($jenisLembur && $jenisLembur->tipe_perhitungan === 'per_jam') {
                                                // Reset ke kosong jika bukan lembur HK
                                                $currentJam = $get('jumlah_jam');
                                                if ($currentJam == 8) {
                                                    $set('jumlah_jam', null);
                                                }
                                            }
                                        }

                                        // Auto calculate upah lembur when jenis lembur changes
                                        $jamLembur = $get('jumlah_jam');
                                        $karyawanId = $get('karyawan_id');

                                        if ($state && $jamLembur && $karyawanId) {
                                            $jenisLembur = \App\Models\JenisLembur::find($state);
                                            $karyawan = \App\Models\Karyawan::find($karyawanId);

                                            if ($jenisLembur && $karyawan) {
                                                // Ambil basis gaji dari penggajian dengan periode 'Basis Gaji'
                                                $basisGaji = $karyawan->penggajian()
                                                    ->where('periode_gaji', 'Basis Gaji')
                                                    ->latest()
                                                    ->first();
                                                $upahBulanan = $basisGaji ? $basisGaji->gaji_pokok : 0;

                                                if ($upahBulanan > 0) {
                                                    $upahLembur = $jenisLembur->hitungUpahLembur($jamLembur, $upahBulanan);
                                                    $set('upah_lembur', $upahLembur);
                                                }
                                            }
                                        }
                                    }),

                                TextInput::make('jumlah_jam')
                                    ->label('Jumlah Jam Lembur')
                                    ->required()
                                    ->numeric()
                                    ->step(0.5)
                                    ->minValue(0.5)
                                    ->maxValue(12)
                                    ->suffix('jam')
                                    ->helperText(function (callable $get) {
                                        $jenisLemburId = $get('jenis_lembur_id');
                                        if ($jenisLemburId) {
                                            $jenisLembur = \App\Models\JenisLembur::find($jenisLemburId);
                                            if ($jenisLembur && $jenisLembur->tipe_perhitungan === 'per_hari') {
                                                return 'Lembur HK otomatis 8 jam (1 hari kerja)';
                                            }
                                        }
                                        return 'Masukkan jumlah jam lembur (minimal 0.5 jam, maksimal 12 jam)';
                                    })
                                    ->disabled(function (callable $get) {
                                        $jenisLemburId = $get('jenis_lembur_id');
                                        if ($jenisLemburId) {
                                            $jenisLembur = \App\Models\JenisLembur::find($jenisLemburId);
                                            return $jenisLembur && $jenisLembur->tipe_perhitungan === 'per_hari';
                                        }
                                        return false;
                                    })
                                    ->dehydrated() // Pastikan nilai tetap disimpan ke database meskipun disabled
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        // Auto calculate upah lembur when jam changes
                                        $jenisLemburId = $get('jenis_lembur_id');
                                        $karyawanId = $get('karyawan_id');

                                        if ($state && $jenisLemburId && $karyawanId) {
                                            $jenisLembur = \App\Models\JenisLembur::find($jenisLemburId);
                                            $karyawan = \App\Models\Karyawan::find($karyawanId);

                                            if ($jenisLembur && $karyawan) {
                                                // Ambil basis gaji dari penggajian dengan periode 'Basis Gaji'
                                                $basisGaji = $karyawan->penggajian()
                                                    ->where('periode_gaji', 'Basis Gaji')
                                                    ->latest()
                                                    ->first();
                                                $upahBulanan = $basisGaji ? $basisGaji->gaji_pokok : 0;

                                                if ($upahBulanan > 0) {
                                                    $upahLembur = $jenisLembur->hitungUpahLembur($state, $upahBulanan);
                                                    $set('upah_lembur', $upahLembur);
                                                }
                                            }
                                        }
                                    })
                                    ->rule(function ($get) {
                                        return function ($attribute, $value, $fail) use ($get) {
                                            // Validasi total jam lembur per bulan tidak boleh melebihi 40 jam
                                            $karyawanId = $get('karyawan_id');
                                            $tanggal = $get('tanggal');

                                            if (!$karyawanId || !$tanggal) {
                                                return;
                                            }

                                            $date = Carbon::parse($tanggal);
                                            $month = $date->month;
                                            $year = $date->year;

                                            // Hitung total jam lembur bulan ini untuk karyawan tersebut
                                            $totalJamBulanIni = Lembur::where('karyawan_id', $karyawanId)
                                                ->whereMonth('tanggal', $month)
                                                ->whereYear('tanggal', $year)
                                                ->sum('jumlah_jam');

                                            // Jika edit, kurangi dengan jam lembur yang sedang diedit
                                            if (request()->route('record')) {
                                                $currentRecord = Lembur::find(request()->route('record'));
                                                if (
                                                    $currentRecord && $currentRecord->karyawan_id == $karyawanId &&
                                                    Carbon::parse($currentRecord->tanggal)->month == $month &&
                                                    Carbon::parse($currentRecord->tanggal)->year == $year
                                                ) {
                                                    $totalJamBulanIni -= $currentRecord->jumlah_jam;
                                                }
                                            }

                                            // Cek apakah total jam lembur melebihi 40 jam
                                            if ($totalJamBulanIni + $value > 40) {
                                                $fail('Total jam lembur karyawan ini di bulan ' . $date->format('F Y') .
                                                    ' akan melebihi 40 jam (saat ini: ' . number_format($totalJamBulanIni, 1) . ' jam).');
                                            }
                                        };
                                    }),

                                TextInput::make('upah_lembur')
                                    ->label('Upah Lembur')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled()
                                    ->dehydrated()
                                    ->formatStateUsing(fn($state) => $state ? number_format($state, 0, ',', '.') : '')
                                    ->helperText('Upah lembur akan dihitung otomatis berdasarkan jenis lembur dan jam'),

                                Placeholder::make('perhitungan_detail')
                                    ->label('Detail Perhitungan')
                                    ->content(function (callable $get) {
                                        $jamLembur = $get('jumlah_jam');
                                        $jenisLemburId = $get('jenis_lembur_id');
                                        $karyawanId = $get('karyawan_id');

                                        if (!$jamLembur || !$jenisLemburId || !$karyawanId) {
                                            return 'Pilih karyawan, jenis lembur, dan masukkan jam untuk melihat perhitungan';
                                        }

                                        try {
                                            $jenisLembur = \App\Models\JenisLembur::find($jenisLemburId);
                                            $karyawan = \App\Models\Karyawan::find($karyawanId);

                                            if (!$jenisLembur || !$karyawan) {
                                                return 'Data tidak ditemukan';
                                            }

                                            // Ambil basis gaji
                                            $basisGaji = $karyawan->penggajian()
                                                ->where('periode_gaji', 'Basis Gaji')
                                                ->latest()
                                                ->first();
                                            $upahBulanan = $basisGaji ? $basisGaji->gaji_pokok : 0;

                                            if ($upahBulanan <= 0) {
                                                return 'Upah bulanan karyawan belum diset';
                                            }

                                            // Get breakdown
                                            $breakdown = $jenisLembur->getBreakdownUpahLembur($jamLembur, $upahBulanan);

                                            $html = '<div class="space-y-2 text-sm">';
                                            $html .= '<div class="font-medium text-gray-700">Perhitungan:</div>';
                                            $html .= '<div>• Upah Bulanan: Rp ' . number_format($upahBulanan, 0, ',', '.') . '</div>';

                                            if ($jenisLembur->tipe_perhitungan === 'per_hari') {
                                                // Perhitungan per hari (HK)
                                                $upahHarian = $upahBulanan / $jenisLembur->pembagi_upah_bulanan;
                                                $html .= '<div>• Upah per Hari: Rp ' . number_format($upahBulanan, 0, ',', '.') . ' ÷ ' . $jenisLembur->pembagi_upah_bulanan . ' hari = Rp ' . number_format($upahHarian, 2, ',', '.') . '</div>';

                                                foreach ($breakdown['detail_perhitungan'] as $detail) {
                                                    $html .= '<div>• ' . $detail['aturan'] . ': 1 hari × ' . $detail['multiplier'] . 'x = Rp ' . number_format($detail['upah'], 0, ',', '.') . '</div>';
                                                }
                                            } else {
                                                // Perhitungan per jam
                                                $upahJam = $upahBulanan / ($jenisLembur->pembagi_upah_bulanan * 8);
                                                $html .= '<div>• Upah per Jam: Rp ' . number_format($upahBulanan, 0, ',', '.') . ' ÷ (' . $jenisLembur->pembagi_upah_bulanan . ' hari × 8 jam) = Rp ' . number_format($upahJam, 2, ',', '.') . '</div>';

                                                foreach ($breakdown['detail_perhitungan'] as $detail) {
                                                    $html .= '<div>• ' . $detail['aturan'] . ': ' . $detail['jam_berlaku'] . ' jam × ' . $detail['multiplier'] . 'x = Rp ' . number_format($detail['upah'], 0, ',', '.') . '</div>';
                                                }
                                            }

                                            $html .= '<div class="font-medium text-green-600 pt-2 border-t">Total: Rp ' . number_format($breakdown['total_upah'], 0, ',', '.') . '</div>';
                                            $html .= '</div>';

                                            return new \Illuminate\Support\HtmlString($html);
                                        } catch (\Exception $e) {
                                            return 'Error dalam perhitungan: ' . $e->getMessage();
                                        }
                                    })
                                    ->visible(fn(callable $get) => $get('jumlah_jam') && $get('jenis_lembur_id') && $get('karyawan_id'))
                                    ->reactive(),

                                Textarea::make('deskripsi')
                                    ->label('Deskripsi Pekerjaan')
                                    ->placeholder('Jelaskan pekerjaan yang dilakukan selama lembur...')
                                    ->rows(3)
                                    ->columnSpanFull(),
                            ]),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('karyawan.nama_lengkap')
                    ->label('Nama Karyawan')
                    ->searchable(['karyawan.nama_lengkap', 'karyawan.nip'])
                    ->sortable()
                    ->formatStateUsing(fn($record) => $record->karyawan->nama_lengkap)
                    ->weight('medium'),

                TextColumn::make('karyawan.departemen.nama_departemen')
                    ->label('Departemen')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('karyawan.divisi.nama_divisi')
                    ->label('Divisi')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('karyawan.jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('tanggal')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('jenisLembur.nama_jenis')
                    ->label('Jenis Lembur')
                    ->badge()
                    ->color(fn($record) => match ($record->jenisLembur?->tipe_perhitungan) {
                        'per_jam' => 'info',
                        'per_hari' => 'success',
                        default => 'gray',
                    })
                    ->placeholder('Belum dipilih')
                    ->toggleable(),

                TextColumn::make('jumlah_jam')
                    ->label('Jam Lembur')
                    ->suffix(' jam')
                    ->alignCenter()
                    ->sortable(),

                TextColumn::make('formatted_upah_lembur')
                    ->label('Upah Lembur')
                    ->alignCenter()
                    ->weight('bold')
                    ->color('success')
                    ->tooltip(function ($record) {
                        $breakdown = $record->getBreakdownUpahLembur();
                        $tooltip = "Breakdown Perhitungan:\n";

                        // Jenis Lembur/Hari
                        if (isset($breakdown['jenis_lembur'])) {
                            $tooltip .= "Jenis Lembur: " . $breakdown['jenis_lembur'] . "\n";
                        } elseif (isset($breakdown['jenis_hari'])) {
                            $tooltip .= "Jenis Hari: " . ucfirst(str_replace('_', ' ', $breakdown['jenis_hari'])) . "\n";
                        } elseif ($record->jenisLembur) {
                            $tooltip .= "Jenis Lembur: " . $record->jenisLembur->nama_jenis . "\n";
                        }

                        // Upah Bulanan
                        if (isset($breakdown['upah_bulanan'])) {
                            $tooltip .= "Upah Bulanan: Rp " . number_format($breakdown['upah_bulanan'], 0, ',', '.') . "\n";
                        }

                        // Tipe Perhitungan
                        $tipePerHitungan = $breakdown['tipe_perhitungan'] ?? 'per_jam';
                        if ($tipePerHitungan === 'per_hari') {
                            $tooltip .= "Lembur HK: 1 hari (8 jam kerja)\n\n";
                        } else {
                            // Jam Lembur
                            if (isset($breakdown['jumlah_jam'])) {
                                $tooltip .= "Jam Lembur: " . $breakdown['jumlah_jam'] . " jam\n\n";
                            }
                        }

                        // Detail Perhitungan
                        if (isset($breakdown['detail_perhitungan']) && is_array($breakdown['detail_perhitungan'])) {
                            foreach ($breakdown['detail_perhitungan'] as $detail) {
                                if (isset($detail['aturan'])) {
                                    $tooltip .= $detail['aturan'] . "\n";
                                }

                                if ($tipePerHitungan === 'per_hari') {
                                    // Untuk lembur HK (per hari)
                                    $tooltip .= "- 1 hari x " . ($detail['multiplier'] ?? 1) . "x\n";
                                } else {
                                    // Untuk lembur per jam
                                    if (isset($detail['jam_berlaku'])) {
                                        $tooltip .= "- " . $detail['jam_berlaku'] . " jam x " . ($detail['multiplier'] ?? 1) . "x\n";
                                    } elseif (isset($detail['multiplier'])) {
                                        $tooltip .= "- Multiplier: " . $detail['multiplier'] . "x\n";
                                    }
                                }

                                if (isset($detail['upah'])) {
                                    $tooltip .= "- Upah: Rp " . number_format($detail['upah'], 0, ',', '.') . "\n\n";
                                }
                            }
                        }

                        // Total Upah
                        if (isset($breakdown['total_upah'])) {
                            $tooltip .= "Total Upah: Rp " . number_format($breakdown['total_upah'], 0, ',', '.');
                        }

                        return $tooltip;
                    }),

                TextColumn::make('deskripsi')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable(),

                TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->toggleable()
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('Tanggal Input')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),

                \EightyNine\Approvals\Tables\Columns\ApprovalStatusColumn::make("approvalStatus.status"),
            ])
            ->filters([
                SelectFilter::make('karyawan_id')
                    ->label('Karyawan')
                    ->relationship(
                        'karyawan',
                        'nama_lengkap',
                        modifyQueryUsing: function ($query) {
                            // Filter berdasarkan permission policy
                            $accessibleIds = \App\Services\PermissionService::getAccessibleKaryawanIds('manage_overtime');
                            return $query->whereIn('id', $accessibleIds);
                        }
                    )
                    ->searchable()
                    ->preload(),

                SelectFilter::make('departemen')
                    ->label('Departemen')
                    ->options(function () {
                        return \App\Models\Departemen::pluck('nama_departemen', 'id');
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn(Builder $query, $value): Builder => $query->whereHas(
                                'karyawan.departemen',
                                fn(Builder $query) => $query->where('id', $value)
                            )
                        );
                    }),

                Filter::make('bulan')
                    ->form([
                        Select::make('month')
                            ->label('Bulan')
                            ->options([
                                1 => 'Januari',
                                2 => 'Februari',
                                3 => 'Maret',
                                4 => 'April',
                                5 => 'Mei',
                                6 => 'Juni',
                                7 => 'Juli',
                                8 => 'Agustus',
                                9 => 'September',
                                10 => 'Oktober',
                                11 => 'November',
                                12 => 'Desember',
                            ])
                            ->default(now()->month),

                        Select::make('year')
                            ->label('Tahun')
                            ->options(function () {
                                $currentYear = now()->year;
                                $years = [];
                                for ($i = $currentYear - 2; $i <= $currentYear + 1; $i++) {
                                    $years[$i] = $i;
                                }
                                return $years;
                            })
                            ->default(now()->year),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['month'],
                                fn(Builder $query, $month): Builder => $query->whereMonth('tanggal', $month)
                            )
                            ->when(
                                $data['year'],
                                fn(Builder $query, $year): Builder => $query->whereYear('tanggal', $year)
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['month'] ?? null) {
                            $monthNames = [
                                1 => 'Januari',
                                2 => 'Februari',
                                3 => 'Maret',
                                4 => 'April',
                                5 => 'Mei',
                                6 => 'Juni',
                                7 => 'Juli',
                                8 => 'Agustus',
                                9 => 'September',
                                10 => 'Oktober',
                                11 => 'November',
                                12 => 'Desember'
                            ];
                            $indicators['month'] = 'Bulan: ' . $monthNames[$data['month']];
                        }
                        if ($data['year'] ?? null) {
                            $indicators['year'] = 'Tahun: ' . $data['year'];
                        }
                        return $indicators;
                    }),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions(
                ApprovalActions::make(
                    // define your action here that will appear once approval is completed
                    Action::make("-")
                ),
            )
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),

                    // Bulk action untuk menghitung total jam lembur
                    BulkAction::make('hitung_total_jam')
                        ->label('Hitung Total Jam')
                        ->icon('heroicon-o-calculator')
                        ->color('success')
                        ->action(function (Collection $records) {
                            $totalJam = $records->sum('jumlah_jam');
                            $jumlahRecord = $records->count();

                            Notification::make()
                                ->title('Total Jam Lembur')
                                ->body("Total {$jumlahRecord} record lembur: {$totalJam} jam")
                                ->success()
                                ->send();
                        })
                        ->deselectRecordsAfterCompletion(),

                    // Bulk action untuk export ke CSV (placeholder)
                    BulkAction::make('export_csv')
                        ->label('Export ke CSV')
                        ->icon('heroicon-o-document-arrow-down')
                        ->color('info')
                        ->action(function (Collection $records) {
                            // Placeholder untuk export functionality
                            Notification::make()
                                ->title('Export CSV')
                                ->body('Fitur export akan segera tersedia')
                                ->info()
                                ->send();
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ])
            ->defaultSort('tanggal', 'desc')
            ->emptyStateHeading('Belum ada data lembur')
            ->emptyStateDescription('Mulai tambahkan data lembur karyawan dengan mengklik tombol "Tambah Lembur".')
            ->emptyStateIcon('heroicon-o-clock');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLemburs::route('/'),
            'create' => Pages\CreateLembur::route('/create'),
            'view' => Pages\ViewLembur::route('/{record}'),
            'edit' => Pages\EditLembur::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        // return parent::getEloquentQuery()
        //     ->withoutGlobalScopes([
        //         SoftDeletingScope::class,
        //     ])
        //     ->with(['karyawan.departemen', 'karyawan.jabatan', 'creator']);

        return PermissionService::applyPermissionFilter(
            parent::getEloquentQuery(),
            'manage_overtime',
            'karyawan_id'
        );
    }

    // Method untuk mendapatkan statistik lembur
    // public static function getStatistik(): array
    // {
    //     $currentMonth = now()->month;
    //     $currentYear = now()->year;

    //     // Get accessible karyawan IDs based on permission
    //     $accessibleIds = \App\Services\PermissionService::getAccessibleKaryawanIds('manage_overtime');

    //     return [
    //         'total_lembur_bulan_ini' => Lembur::whereMonth('tanggal', $currentMonth)
    //             ->whereYear('tanggal', $currentYear)
    //             ->whereIn('karyawan_id', $accessibleIds)
    //             ->sum('jumlah_jam'),
    //         'total_record_bulan_ini' => Lembur::whereMonth('tanggal', $currentMonth)
    //             ->whereYear('tanggal', $currentYear)
    //             ->whereIn('karyawan_id', $accessibleIds)
    //             ->count(),
    //         'rata_rata_jam_per_hari' => Lembur::whereMonth('tanggal', $currentMonth)
    //             ->whereYear('tanggal', $currentYear)
    //             ->whereIn('karyawan_id', $accessibleIds)
    //             ->avg('jumlah_jam'),
    //         'karyawan_terbanyak_lembur' => Lembur::with('karyawan')
    //             ->whereMonth('tanggal', $currentMonth)
    //             ->whereYear('tanggal', $currentYear)
    //             ->whereIn('karyawan_id', $accessibleIds)
    //             ->selectRaw('karyawan_id, SUM(jumlah_jam) as total_jam')
    //             ->groupBy('karyawan_id')
    //             ->orderByDesc('total_jam')
    //             ->first(),
    //     ];
    // }
}
