<?php

namespace Database\Seeders;

use App\Models\Customer;
use App\Models\Karyawan;
use App\Models\Outlet;
use App\Models\PosTransaction;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use League\Csv\Reader;

class POSTransactionsVieraQuinosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $csv = Reader::createFromPath(database_path('seeders/data/pos_transactions_viera.csv'), 'r');
        $csv->setHeaderOffset(0);

        $count = $csv->count();
        $bar = $this->command->getOutput()->createProgressBar($count);
        $bar->start();

        $cust_umum = Customer::where('nama', 'UMUM')->first();
        if (!$cust_umum) {
            $cust_umum = Customer::create([
                'nama' => 'UMUM',
                'alamat' => 'UMUM',
                'is_active' => true,
            ]);
        }

        $kasir_umum = User::where('name', 'KASIR UMUM')->first();
        if (!$kasir_umum) {
            $kasir_umum = User::create([
                'name' => 'KASIR UMUM',
                'email' => '<EMAIL>',
                'password' => Hash::make('kasirumum'),
                'role' => 'karyawan',
                'karyawan_id' => (Karyawan::where('nama_lengkap', 'KASIR UMUM')->first() ?? Karyawan::create([
                    'nama_lengkap' => 'KASIR UMUM',
                    'email' => '<EMAIL>',
                ]))->id,
            ]);
        }

        $outlet_default = Outlet::where('name', 'RKVPUSAT')->first();
        if (!$outlet_default) {
            $outlet_default = Outlet::create([
                'name' => 'RKVPUSAT',
                'code' => 'RKVPUSAT',
                'type' => 'toko',
                'category' => 'VOO',
                'address' => 'Jl. Melati',
                'city' => 'Pekanbaru',
                'opening_date' => '2015-01-01',
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'delivery_radius' => 5000,
                'tax_rate' => 11.00,
                'service_charge_rate' => 0.00,
                'status' => 'active',
                'description' => 'Outlet utama di Pekanbaru dengan menu lengkap',
                'is_active' => true,
            ]);
        }

        try {
            DB::transaction(function () use ($csv, $bar, $cust_umum, $kasir_umum, $outlet_default) {
                foreach ($csv->getRecords() as $data) {
                    if (!PosTransaction::find($data["id"])) {
                        if (!$data['customer_name'] || strtoupper($data['customer_name']) == 'UMUM') {
                            $data['customer_id'] = $cust_umum->id;
                        } else {
                            $data['customer_id'] = (Customer::where('nama', $data['customer_name'])->first() ?? $cust_umum)->id;
                        }
                        if (!$data['user_name'] || strtoupper($data['user_name']) == 'UMUM') {
                            $data['user_id'] = $kasir_umum->id;
                        } else {
                            $data['user_id'] = (User::where('name', $data['user_name'])->first() ?? $kasir_umum)->id;
                        }
                        $data['outlet_id'] = $outlet_default->id;
                        unset($data['customer_name']);
                        unset($data['user_name']);
                        $data['payment_method'] = $data['payment_method'] ?: 'cash';

                        PosTransaction::create($data);
                    }
                    $bar->advance();
                }
            });

            $bar->finish();
            $this->command->newLine();
            $this->command->info("✅ Selesai insert {$count} data PosTransaction");
        } catch (\Throwable $e) {
            $bar->clear();
            $this->command->error("❌ Gagal insert data: " . $e->getMessage());
        }
    }
}
