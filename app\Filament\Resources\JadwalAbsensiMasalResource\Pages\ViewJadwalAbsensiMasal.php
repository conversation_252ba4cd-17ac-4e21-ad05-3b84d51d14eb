<?php

namespace App\Filament\Resources\JadwalAbsensiMasalResource\Pages;

use App\Filament\Resources\JadwalAbsensiMasalResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;

class ViewJadwalAbsensiMasal extends ViewRecord
{
    protected static string $resource = JadwalAbsensiMasalResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('generate')
                ->label('Generate Jadwal & Absensi')
                ->icon('heroicon-o-play')
                ->color('success')
                ->visible(fn() => !$this->getRecord()->isGenerated())
                ->requiresConfirmation()
                ->modalHeading('Generate Jadwal & Absensi')
                ->modalDescription(function () {
                    $record = $this->getRecord();
                    $totalDays = $record->total_days;
                    $totalKaryawan = $record->karyawan()->count();
                    $totalExpected = $record->total_expected_records;
                    
                    return "Proses ini akan membuat:\n" .
                           "• {$totalDays} hari kerja\n" .
                           "• {$totalKaryawan} karyawan\n" .
                           "• Sekitar {$totalExpected} record total\n\n" .
                           "Apakah Anda yakin ingin melanjutkan?";
                })
                ->action(function () {
                    $results = $this->getRecord()->generateJadwalDanAbsensi();
                    
                    if (!empty($results['errors'])) {
                        Notification::make()
                            ->title('Generate Gagal')
                            ->body(implode(', ', $results['errors']))
                            ->danger()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('Generate Berhasil')
                            ->body("Berhasil membuat {$results['schedules_created']} jadwal dan {$results['attendance_created']} record absensi")
                            ->success()
                            ->send();
                    }
                }),

            Actions\EditAction::make()
                ->visible(fn() => !$this->getRecord()->isGenerated()),

            Actions\DeleteAction::make()
                ->visible(fn() => !$this->getRecord()->isGenerated()),

            Actions\Action::make('back')
                ->label('Kembali')
                ->url($this->getResource()::getUrl('index'))
                ->icon('heroicon-o-arrow-left')
                ->color('gray'),
        ];
    }

    public function getTitle(): string
    {
        return 'Detail Jadwal & Absensi Masal: ' . $this->getRecord()->nama_jadwal;
    }
}
