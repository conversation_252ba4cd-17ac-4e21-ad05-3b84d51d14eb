<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.manufacturing.resources.bom-resource.pages.create-bom' => 'App\\Filament\\Manufacturing\\Resources\\BomResource\\Pages\\CreateBom',
    'app.filament.manufacturing.resources.bom-resource.pages.edit-bom' => 'App\\Filament\\Manufacturing\\Resources\\BomResource\\Pages\\EditBom',
    'app.filament.manufacturing.resources.bom-resource.pages.list-boms' => 'App\\Filament\\Manufacturing\\Resources\\BomResource\\Pages\\ListBoms',
    'app.filament.manufacturing.resources.bom-resource.pages.view-bom' => 'App\\Filament\\Manufacturing\\Resources\\BomResource\\Pages\\ViewBom',
    'app.filament.manufacturing.resources.bom-resource.relation-managers.bom-items-relation-manager' => 'App\\Filament\\Manufacturing\\Resources\\BomResource\\RelationManagers\\BomItemsRelationManager',
    'app.filament.manufacturing.resources.equipment-maintenance-resource.pages.create-equipment-maintenance' => 'App\\Filament\\Manufacturing\\Resources\\EquipmentMaintenanceResource\\Pages\\CreateEquipmentMaintenance',
    'app.filament.manufacturing.resources.equipment-maintenance-resource.pages.edit-equipment-maintenance' => 'App\\Filament\\Manufacturing\\Resources\\EquipmentMaintenanceResource\\Pages\\EditEquipmentMaintenance',
    'app.filament.manufacturing.resources.equipment-maintenance-resource.pages.list-equipment-maintenances' => 'App\\Filament\\Manufacturing\\Resources\\EquipmentMaintenanceResource\\Pages\\ListEquipmentMaintenances',
    'app.filament.manufacturing.resources.material-consumption-resource.pages.create-material-consumption' => 'App\\Filament\\Manufacturing\\Resources\\MaterialConsumptionResource\\Pages\\CreateMaterialConsumption',
    'app.filament.manufacturing.resources.material-consumption-resource.pages.edit-material-consumption' => 'App\\Filament\\Manufacturing\\Resources\\MaterialConsumptionResource\\Pages\\EditMaterialConsumption',
    'app.filament.manufacturing.resources.material-consumption-resource.pages.list-material-consumptions' => 'App\\Filament\\Manufacturing\\Resources\\MaterialConsumptionResource\\Pages\\ListMaterialConsumptions',
    'app.filament.manufacturing.resources.material-consumption-resource.pages.view-material-consumption' => 'App\\Filament\\Manufacturing\\Resources\\MaterialConsumptionResource\\Pages\\ViewMaterialConsumption',
    'app.filament.manufacturing.resources.production-expense-resource.pages.create-production-expense' => 'App\\Filament\\Manufacturing\\Resources\\ProductionExpenseResource\\Pages\\CreateProductionExpense',
    'app.filament.manufacturing.resources.production-expense-resource.pages.edit-production-expense' => 'App\\Filament\\Manufacturing\\Resources\\ProductionExpenseResource\\Pages\\EditProductionExpense',
    'app.filament.manufacturing.resources.production-expense-resource.pages.list-production-expenses' => 'App\\Filament\\Manufacturing\\Resources\\ProductionExpenseResource\\Pages\\ListProductionExpenses',
    'app.filament.manufacturing.resources.production-order-resource.pages.create-production-order' => 'App\\Filament\\Manufacturing\\Resources\\ProductionOrderResource\\Pages\\CreateProductionOrder',
    'app.filament.manufacturing.resources.production-order-resource.pages.edit-production-order' => 'App\\Filament\\Manufacturing\\Resources\\ProductionOrderResource\\Pages\\EditProductionOrder',
    'app.filament.manufacturing.resources.production-order-resource.pages.list-production-orders' => 'App\\Filament\\Manufacturing\\Resources\\ProductionOrderResource\\Pages\\ListProductionOrders',
    'app.filament.manufacturing.resources.production-order-resource.pages.view-production-order' => 'App\\Filament\\Manufacturing\\Resources\\ProductionOrderResource\\Pages\\ViewProductionOrder',
    'app.filament.manufacturing.resources.production-order-resource.relation-managers.production-order-materials-relation-manager' => 'App\\Filament\\Manufacturing\\Resources\\ProductionOrderResource\\RelationManagers\\ProductionOrderMaterialsRelationManager',
    'app.filament.manufacturing.resources.production-order-resource.relation-managers.production-stages-relation-manager' => 'App\\Filament\\Manufacturing\\Resources\\ProductionOrderResource\\RelationManagers\\ProductionStagesRelationManager',
    'app.filament.manufacturing.resources.production-order-resource.relation-managers.quality-inspections-relation-manager' => 'App\\Filament\\Manufacturing\\Resources\\ProductionOrderResource\\RelationManagers\\QualityInspectionsRelationManager',
    'app.filament.manufacturing.resources.production-plan-resource.pages.create-production-plan' => 'App\\Filament\\Manufacturing\\Resources\\ProductionPlanResource\\Pages\\CreateProductionPlan',
    'app.filament.manufacturing.resources.production-plan-resource.pages.edit-production-plan' => 'App\\Filament\\Manufacturing\\Resources\\ProductionPlanResource\\Pages\\EditProductionPlan',
    'app.filament.manufacturing.resources.production-plan-resource.pages.list-production-plans' => 'App\\Filament\\Manufacturing\\Resources\\ProductionPlanResource\\Pages\\ListProductionPlans',
    'app.filament.manufacturing.resources.production-plan-resource.pages.view-production-plan' => 'App\\Filament\\Manufacturing\\Resources\\ProductionPlanResource\\Pages\\ViewProductionPlan',
    'app.filament.manufacturing.resources.production-plan-resource.relation-managers.production-orders-relation-manager' => 'App\\Filament\\Manufacturing\\Resources\\ProductionPlanResource\\RelationManagers\\ProductionOrdersRelationManager',
    'app.filament.manufacturing.resources.production-plan-resource.relation-managers.production-schedules-relation-manager' => 'App\\Filament\\Manufacturing\\Resources\\ProductionPlanResource\\RelationManagers\\ProductionSchedulesRelationManager',
    'app.filament.manufacturing.resources.production-transaction-resource.pages.create-production-transaction' => 'App\\Filament\\Manufacturing\\Resources\\ProductionTransactionResource\\Pages\\CreateProductionTransaction',
    'app.filament.manufacturing.resources.production-transaction-resource.pages.edit-production-transaction' => 'App\\Filament\\Manufacturing\\Resources\\ProductionTransactionResource\\Pages\\EditProductionTransaction',
    'app.filament.manufacturing.resources.production-transaction-resource.pages.list-production-transactions' => 'App\\Filament\\Manufacturing\\Resources\\ProductionTransactionResource\\Pages\\ListProductionTransactions',
    'app.filament.manufacturing.resources.quality-control-point-resource.pages.create-quality-control-point' => 'App\\Filament\\Manufacturing\\Resources\\QualityControlPointResource\\Pages\\CreateQualityControlPoint',
    'app.filament.manufacturing.resources.quality-control-point-resource.pages.edit-quality-control-point' => 'App\\Filament\\Manufacturing\\Resources\\QualityControlPointResource\\Pages\\EditQualityControlPoint',
    'app.filament.manufacturing.resources.quality-control-point-resource.pages.list-quality-control-points' => 'App\\Filament\\Manufacturing\\Resources\\QualityControlPointResource\\Pages\\ListQualityControlPoints',
    'app.filament.manufacturing.resources.quality-control-point-resource.pages.view-quality-control-point' => 'App\\Filament\\Manufacturing\\Resources\\QualityControlPointResource\\Pages\\ViewQualityControlPoint',
    'app.filament.manufacturing.resources.quality-inspection-resource.pages.create-quality-inspection' => 'App\\Filament\\Manufacturing\\Resources\\QualityInspectionResource\\Pages\\CreateQualityInspection',
    'app.filament.manufacturing.resources.quality-inspection-resource.pages.edit-quality-inspection' => 'App\\Filament\\Manufacturing\\Resources\\QualityInspectionResource\\Pages\\EditQualityInspection',
    'app.filament.manufacturing.resources.quality-inspection-resource.pages.list-quality-inspections' => 'App\\Filament\\Manufacturing\\Resources\\QualityInspectionResource\\Pages\\ListQualityInspections',
    'app.filament.manufacturing.resources.quality-inspection-resource.pages.view-quality-inspection' => 'App\\Filament\\Manufacturing\\Resources\\QualityInspectionResource\\Pages\\ViewQualityInspection',
    'app.filament.manufacturing.resources.quality-inspection-resource.relation-managers.quality-tests-relation-manager' => 'App\\Filament\\Manufacturing\\Resources\\QualityInspectionResource\\RelationManagers\\QualityTestsRelationManager',
    'app.filament.manufacturing.resources.shop-floor-control-resource.pages.create-shop-floor-control' => 'App\\Filament\\Manufacturing\\Resources\\ShopFloorControlResource\\Pages\\CreateShopFloorControl',
    'app.filament.manufacturing.resources.shop-floor-control-resource.pages.edit-shop-floor-control' => 'App\\Filament\\Manufacturing\\Resources\\ShopFloorControlResource\\Pages\\EditShopFloorControl',
    'app.filament.manufacturing.resources.shop-floor-control-resource.pages.list-shop-floor-controls' => 'App\\Filament\\Manufacturing\\Resources\\ShopFloorControlResource\\Pages\\ListShopFloorControls',
    'app.filament.manufacturing.pages.dashboard' => 'App\\Filament\\Manufacturing\\Pages\\Dashboard',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'app.filament.manufacturing.widgets.cost-analysis-widget' => 'App\\Filament\\Manufacturing\\Widgets\\CostAnalysisWidget',
    'app.filament.manufacturing.widgets.production-efficiency-widget' => 'App\\Filament\\Manufacturing\\Widgets\\ProductionEfficiencyWidget',
    'app.filament.manufacturing.widgets.production-orders-stats-widget' => 'App\\Filament\\Manufacturing\\Widgets\\ProductionOrdersStatsWidget',
    'app.filament.manufacturing.widgets.production-overview-widget' => 'App\\Filament\\Manufacturing\\Widgets\\ProductionOverviewWidget',
    'app.filament.manufacturing.widgets.quality-control-stats-widget' => 'App\\Filament\\Manufacturing\\Widgets\\QualityControlStatsWidget',
    'app.filament.manufacturing.widgets.recent-production-orders-widget' => 'App\\Filament\\Manufacturing\\Widgets\\RecentProductionOrdersWidget',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Pages\\Dashboard.php' => 'App\\Filament\\Manufacturing\\Pages\\Dashboard',
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Manufacturing/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Manufacturing\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Resources\\BomResource.php' => 'App\\Filament\\Manufacturing\\Resources\\BomResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Resources\\EquipmentMaintenanceResource.php' => 'App\\Filament\\Manufacturing\\Resources\\EquipmentMaintenanceResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Resources\\MaterialConsumptionResource.php' => 'App\\Filament\\Manufacturing\\Resources\\MaterialConsumptionResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Resources\\ProductionExpenseResource.php' => 'App\\Filament\\Manufacturing\\Resources\\ProductionExpenseResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Resources\\ProductionOrderResource.php' => 'App\\Filament\\Manufacturing\\Resources\\ProductionOrderResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Resources\\ProductionPlanResource.php' => 'App\\Filament\\Manufacturing\\Resources\\ProductionPlanResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Resources\\ProductionTransactionResource.php' => 'App\\Filament\\Manufacturing\\Resources\\ProductionTransactionResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Resources\\QualityControlPointResource.php' => 'App\\Filament\\Manufacturing\\Resources\\QualityControlPointResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Resources\\QualityInspectionResource.php' => 'App\\Filament\\Manufacturing\\Resources\\QualityInspectionResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Resources\\ShopFloorControlResource.php' => 'App\\Filament\\Manufacturing\\Resources\\ShopFloorControlResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Manufacturing/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Manufacturing\\Resources',
  ),
  'widgets' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Widgets\\CostAnalysisWidget.php' => 'App\\Filament\\Manufacturing\\Widgets\\CostAnalysisWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Widgets\\ProductionEfficiencyWidget.php' => 'App\\Filament\\Manufacturing\\Widgets\\ProductionEfficiencyWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Widgets\\ProductionOrdersStatsWidget.php' => 'App\\Filament\\Manufacturing\\Widgets\\ProductionOrdersStatsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Widgets\\ProductionOverviewWidget.php' => 'App\\Filament\\Manufacturing\\Widgets\\ProductionOverviewWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Widgets\\QualityControlStatsWidget.php' => 'App\\Filament\\Manufacturing\\Widgets\\QualityControlStatsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Manufacturing\\Widgets\\RecentProductionOrdersWidget.php' => 'App\\Filament\\Manufacturing\\Widgets\\RecentProductionOrdersWidget',
    0 => 'Filament\\Widgets\\AccountWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Manufacturing/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Manufacturing\\Widgets',
  ),
);