<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JadwalAbsensiMasalResource\Pages;
use App\Models\JadwalAbsensiMasal;
use App\Models\Shift;
use App\Models\Entitas;
use App\Models\Karyawan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class JadwalAbsensiMasalResource extends Resource
{
    protected static ?string $model = JadwalAbsensiMasal::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationLabel = 'Jadwal & Absensi Masal';

    protected static ?string $modelLabel = 'Jadwal & Absensi Masal';

    protected static ?string $pluralModelLabel = 'Jadwal & Absensi Masal';

    protected static ?string $navigationGroup = 'Manajemen Jadwal';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Jadwal & Absensi Masal')
                    ->schema([
                        Forms\Components\TextInput::make('nama_jadwal')
                            ->label('Nama Jadwal')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Contoh: Jadwal Januari 2025 - Shift Pagi'),

                        Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('tanggal_mulai')
                                    ->label('Tanggal Mulai')
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, $state) {
                                        // Auto set tanggal_selesai to end of month if not set
                                        if ($state) {
                                            $endOfMonth = \Carbon\Carbon::parse($state)->endOfMonth()->format('Y-m-d');
                                            $set('tanggal_selesai', $endOfMonth);
                                        }
                                    }),

                                Forms\Components\DatePicker::make('tanggal_selesai')
                                    ->label('Tanggal Selesai')
                                    ->required()
                                    ->afterOrEqual('tanggal_mulai'),
                            ]),

                        Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('shift_id')
                                    ->label('Shift')
                                    ->options(Shift::where('is_active', true)->pluck('nama_shift', 'id'))
                                    ->required()
                                    ->searchable()
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, $state) {
                                        if ($state) {
                                            $shift = Shift::find($state);
                                            if ($shift) {
                                                $description = "Waktu: {$shift->waktu_mulai} - {$shift->waktu_selesai}";
                                                if ($shift->isSplitShift()) {
                                                    $description .= " (Split: {$shift->waktu_mulai_periode2} - {$shift->waktu_selesai_periode2})";
                                                }
                                                $set('shift_description', $description);
                                            }
                                        }
                                    }),

                                Forms\Components\Select::make('entitas_id')
                                    ->label('Entitas/Lokasi')
                                    ->options(Entitas::where('is_active', true)->pluck('nama', 'id'))
                                    ->searchable()
                                    ->nullable()
                                    ->helperText('Pilih entitas/lokasi kerja. Kosongkan jika berlaku untuk semua entitas.'),
                            ]),

                        Forms\Components\Placeholder::make('shift_description')
                            ->label('Detail Shift')
                            ->content(function (callable $get) {
                                $shiftId = $get('shift_id');
                                if ($shiftId) {
                                    $shift = Shift::find($shiftId);
                                    if ($shift) {
                                        $description = "Waktu: {$shift->waktu_mulai} - {$shift->waktu_selesai}";
                                        if ($shift->isSplitShift()) {
                                            $description .= "\nSplit Shift - Periode 2: {$shift->waktu_mulai_periode2} - {$shift->waktu_selesai_periode2}";
                                        }
                                        return $description;
                                    }
                                }
                                return 'Pilih shift untuk melihat detail waktu';
                            }),

                        Forms\Components\Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->maxLength(1000)
                            ->columnSpanFull(),
                    ]),

                Section::make('Pilih Karyawan')
                    ->schema([
                        Forms\Components\CheckboxList::make('karyawan')
                            ->label('Karyawan')
                            ->relationship('karyawan', 'nama_lengkap')
                            ->options(function (callable $get) {
                                $entitasId = $get('entitas_id');
                                $query = Karyawan::where('status_aktif', true);

                                if ($entitasId) {
                                    $query->where('id_entitas', $entitasId);
                                }

                                return $query->pluck('nama_lengkap', 'id');
                            })
                            ->searchable()
                            ->bulkToggleable()
                            ->required()
                            ->columns(3)
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, $state) {
                                if (is_array($state)) {
                                    $set('total_karyawan', count($state));
                                }
                            }),

                        Forms\Components\Placeholder::make('total_karyawan')
                            ->label('Total Karyawan Dipilih')
                            ->content(function (callable $get) {
                                $karyawan = $get('karyawan');
                                if (is_array($karyawan)) {
                                    return count($karyawan) . ' karyawan';
                                }
                                return '0 karyawan';
                            }),
                    ]),

                Section::make('Estimasi')
                    ->schema([
                        Forms\Components\Placeholder::make('estimasi_jadwal')
                            ->label('Estimasi Total Record')
                            ->content(function (callable $get) {
                                $tanggalMulai = $get('tanggal_mulai');
                                $tanggalSelesai = $get('tanggal_selesai');
                                $karyawan = $get('karyawan');
                                $shiftId = $get('shift_id');

                                if ($tanggalMulai && $tanggalSelesai && is_array($karyawan) && $shiftId) {
                                    $totalDays = \Carbon\Carbon::parse($tanggalMulai)->diffInDays(\Carbon\Carbon::parse($tanggalSelesai)) + 1;
                                    $totalKaryawan = count($karyawan);

                                    $shift = Shift::find($shiftId);
                                    $multiplier = $shift && $shift->isSplitShift() ? 3 : 2; // 1 jadwal + 1 atau 2 absensi

                                    $totalJadwal = $totalDays * $totalKaryawan;
                                    $totalAbsensi = $totalDays * $totalKaryawan * ($shift && $shift->isSplitShift() ? 2 : 1);

                                    return "Jadwal: {$totalJadwal} record\nAbsensi: {$totalAbsensi} record\nTotal: " . ($totalJadwal + $totalAbsensi) . " record";
                                }

                                return 'Lengkapi form untuk melihat estimasi';
                            }),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nama_jadwal')
                    ->label('Nama Jadwal')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('tanggal_mulai')
                    ->label('Periode')
                    ->formatStateUsing(function ($record) {
                        return $record->tanggal_mulai->format('d M Y') . ' - ' . $record->tanggal_selesai->format('d M Y');
                    })
                    ->sortable(),

                TextColumn::make('shift.nama_shift')
                    ->label('Shift')
                    ->badge()
                    ->color('info'),

                TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->placeholder('Semua Entitas')
                    ->toggleable(),

                TextColumn::make('karyawan_count')
                    ->label('Jumlah Karyawan')
                    ->getStateUsing(function ($record) {
                        return $record->karyawan()->count();
                    })
                    ->badge()
                    ->color('success'),

                TextColumn::make('total_days')
                    ->label('Total Hari')
                    ->badge()
                    ->color('warning'),

                BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'secondary' => 'draft',
                        'success' => 'completed',
                        'danger' => 'cancelled',
                    ]),

                TextColumn::make('generated_at')
                    ->label('Di-generate')
                    ->dateTime('d M Y H:i')
                    ->placeholder('Belum di-generate')
                    ->toggleable(),

                TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),

                SelectFilter::make('shift_id')
                    ->label('Shift')
                    ->relationship('shift', 'nama_shift'),

                SelectFilter::make('entitas_id')
                    ->label('Entitas')
                    ->relationship('entitas', 'nama'),

                Filter::make('tanggal_mulai')
                    ->form([
                        DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_mulai', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_selesai', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('generate')
                    ->label('Generate')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->visible(fn($record) => !$record->isGenerated())
                    ->requiresConfirmation()
                    ->modalHeading('Generate Jadwal & Absensi')
                    ->modalDescription('Apakah Anda yakin ingin men-generate jadwal dan absensi untuk periode ini? Proses ini akan membuat jadwal kerja dan record absensi otomatis.')
                    ->action(function ($record) {
                        $results = $record->generateJadwalDanAbsensi();

                        if (!empty($results['errors'])) {
                            Notification::make()
                                ->title('Generate Gagal')
                                ->body(implode(', ', $results['errors']))
                                ->danger()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Generate Berhasil')
                                ->body("Berhasil membuat {$results['schedules_created']} jadwal dan {$results['attendance_created']} record absensi")
                                ->success()
                                ->send();
                        }
                    }),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => !$record->isGenerated()),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn($record) => !$record->isGenerated()),
            ])
            // ->bulkActions([
            //     Tables\Actions\BulkActionGroup::make([
            //         Tables\Actions\DeleteBulkAction::make()
            //             ->visible(fn($records) => $records->every(fn($record) => !$record->isGenerated())),
            //     ]),
            // ])
            ->defaultSort('created_at', 'desc')
            ->defaultPaginationPageOption(25)
            ->paginationPageOptions([10, 25, 50, 100]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                'shift:id,nama_shift,waktu_mulai,waktu_selesai,is_split_shift,waktu_mulai_periode2,waktu_selesai_periode2',
                'entitas:id,nama',
                'creator:id,name'
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJadwalAbsensiMasals::route('/'),
            'create' => Pages\CreateJadwalAbsensiMasal::route('/create'),
            'view' => Pages\ViewJadwalAbsensiMasal::route('/{record}'),
            'edit' => Pages\EditJadwalAbsensiMasal::route('/{record}/edit'),
        ];
    }
}
