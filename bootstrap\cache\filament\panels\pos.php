<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.pos.resources.category-resource.pages.create-category' => 'App\\Filament\\Pos\\Resources\\CategoryResource\\Pages\\CreateCategory',
    'app.filament.pos.resources.category-resource.pages.edit-category' => 'App\\Filament\\Pos\\Resources\\CategoryResource\\Pages\\EditCategory',
    'app.filament.pos.resources.category-resource.pages.list-categories' => 'App\\Filament\\Pos\\Resources\\CategoryResource\\Pages\\ListCategories',
    'app.filament.pos.resources.category-resource.pages.view-category' => 'App\\Filament\\Pos\\Resources\\CategoryResource\\Pages\\ViewCategory',
    'app.filament.pos.resources.category-resource.relation-managers.products-relation-manager' => 'App\\Filament\\Pos\\Resources\\CategoryResource\\RelationManagers\\ProductsRelationManager',
    'app.filament.pos.resources.customer-resource.pages.create-customer' => 'App\\Filament\\Pos\\Resources\\CustomerResource\\Pages\\CreateCustomer',
    'app.filament.pos.resources.customer-resource.pages.edit-customer' => 'App\\Filament\\Pos\\Resources\\CustomerResource\\Pages\\EditCustomer',
    'app.filament.pos.resources.customer-resource.pages.list-customers' => 'App\\Filament\\Pos\\Resources\\CustomerResource\\Pages\\ListCustomers',
    'app.filament.pos.resources.customer-resource.pages.view-customer' => 'App\\Filament\\Pos\\Resources\\CustomerResource\\Pages\\ViewCustomer',
    'app.filament.pos.resources.customer-resource.relation-managers.loyalty-transactions-relation-manager' => 'App\\Filament\\Pos\\Resources\\CustomerResource\\RelationManagers\\LoyaltyTransactionsRelationManager',
    'app.filament.pos.resources.customer-resource.relation-managers.pos-transactions-relation-manager' => 'App\\Filament\\Pos\\Resources\\CustomerResource\\RelationManagers\\PosTransactionsRelationManager',
    'app.filament.pos.resources.outlet-resource.pages.create-outlet' => 'App\\Filament\\Pos\\Resources\\OutletResource\\Pages\\CreateOutlet',
    'app.filament.pos.resources.outlet-resource.pages.edit-outlet' => 'App\\Filament\\Pos\\Resources\\OutletResource\\Pages\\EditOutlet',
    'app.filament.pos.resources.outlet-resource.pages.list-outlets' => 'App\\Filament\\Pos\\Resources\\OutletResource\\Pages\\ListOutlets',
    'app.filament.pos.resources.outlet-resource.relation-managers.assigned-users-relation-manager' => 'App\\Filament\\Pos\\Resources\\OutletResource\\RelationManagers\\AssignedUsersRelationManager',
    'app.filament.pos.resources.outlet-resource.relation-managers.pos-transactions-relation-manager' => 'App\\Filament\\Pos\\Resources\\OutletResource\\RelationManagers\\PosTransactionsRelationManager',
    'app.filament.pos.resources.pos-transaction-resource.pages.create-pos-transaction' => 'App\\Filament\\Pos\\Resources\\PosTransactionResource\\Pages\\CreatePosTransaction',
    'app.filament.pos.resources.pos-transaction-resource.pages.edit-pos-transaction' => 'App\\Filament\\Pos\\Resources\\PosTransactionResource\\Pages\\EditPosTransaction',
    'app.filament.pos.resources.pos-transaction-resource.pages.list-pos-transactions' => 'App\\Filament\\Pos\\Resources\\PosTransactionResource\\Pages\\ListPosTransactions',
    'app.filament.pos.resources.pos-transaction-resource.pages.view-pos-transaction' => 'App\\Filament\\Pos\\Resources\\PosTransactionResource\\Pages\\ViewPosTransaction',
    'app.filament.pos.resources.pos-transaction-resource.relation-managers.pos-transaction-items-relation-manager' => 'App\\Filament\\Pos\\Resources\\PosTransactionResource\\RelationManagers\\PosTransactionItemsRelationManager',
    'app.filament.pos.resources.price-list-resource.pages.create-price-list' => 'App\\Filament\\Pos\\Resources\\PriceListResource\\Pages\\CreatePriceList',
    'app.filament.pos.resources.price-list-resource.pages.edit-price-list' => 'App\\Filament\\Pos\\Resources\\PriceListResource\\Pages\\EditPriceList',
    'app.filament.pos.resources.price-list-resource.pages.list-price-lists' => 'App\\Filament\\Pos\\Resources\\PriceListResource\\Pages\\ListPriceLists',
    'app.filament.pos.resources.price-list-resource.relation-managers.outlets-relation-manager' => 'App\\Filament\\Pos\\Resources\\PriceListResource\\RelationManagers\\OutletsRelationManager',
    'app.filament.pos.resources.price-list-resource.relation-managers.price-list-items-relation-manager' => 'App\\Filament\\Pos\\Resources\\PriceListResource\\RelationManagers\\PriceListItemsRelationManager',
    'app.filament.pos.resources.product-resource.pages.create-product' => 'App\\Filament\\Pos\\Resources\\ProductResource\\Pages\\CreateProduct',
    'app.filament.pos.resources.product-resource.pages.edit-product' => 'App\\Filament\\Pos\\Resources\\ProductResource\\Pages\\EditProduct',
    'app.filament.pos.resources.product-resource.pages.list-products' => 'App\\Filament\\Pos\\Resources\\ProductResource\\Pages\\ListProducts',
    'app.filament.pos.resources.product-resource.pages.view-product' => 'App\\Filament\\Pos\\Resources\\ProductResource\\Pages\\ViewProduct',
    'app.filament.pos.resources.product-resource.relation-managers.pos-transaction-items-relation-manager' => 'App\\Filament\\Pos\\Resources\\ProductResource\\RelationManagers\\PosTransactionItemsRelationManager',
    'app.filament.pos.pages.dashboard' => 'App\\Filament\\Pos\\Pages\\Dashboard',
    'app.filament.pos.widgets.hourly-sales-widget' => 'App\\Filament\\Pos\\Widgets\\HourlySalesWidget',
    'app.filament.pos.widgets.location-performance-widget' => 'App\\Filament\\Pos\\Widgets\\LocationPerformanceWidget',
    'app.filament.pos.widgets.outlet-assignment-overview-widget' => 'App\\Filament\\Pos\\Widgets\\OutletAssignmentOverviewWidget',
    'app.filament.pos.widgets.payment-methods-widget' => 'App\\Filament\\Pos\\Widgets\\PaymentMethodsWidget',
    'app.filament.pos.widgets.recent-transactions-widget' => 'App\\Filament\\Pos\\Widgets\\RecentTransactionsWidget',
    'app.filament.pos.widgets.revenue-analytics-widget' => 'App\\Filament\\Pos\\Widgets\\RevenueAnalyticsWidget',
    'app.filament.pos.widgets.sales-overview-widget' => 'App\\Filament\\Pos\\Widgets\\SalesOverviewWidget',
    'app.filament.pos.widgets.top-products-widget' => 'App\\Filament\\Pos\\Widgets\\TopProductsWidget',
    'app.filament.pos.widgets.welcome-widget' => 'App\\Filament\\Pos\\Widgets\\WelcomeWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Pages\\Dashboard.php' => 'App\\Filament\\Pos\\Pages\\Dashboard',
    0 => 'App\\Filament\\Pos\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Pos/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Pos\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Resources\\CategoryResource.php' => 'App\\Filament\\Pos\\Resources\\CategoryResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Resources\\CustomerResource.php' => 'App\\Filament\\Pos\\Resources\\CustomerResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Resources\\OutletResource.php' => 'App\\Filament\\Pos\\Resources\\OutletResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Resources\\PosTransactionResource.php' => 'App\\Filament\\Pos\\Resources\\PosTransactionResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Resources\\PriceListResource.php' => 'App\\Filament\\Pos\\Resources\\PriceListResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Resources\\ProductResource.php' => 'App\\Filament\\Pos\\Resources\\ProductResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Pos/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Pos\\Resources',
  ),
  'widgets' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Widgets\\HourlySalesWidget.php' => 'App\\Filament\\Pos\\Widgets\\HourlySalesWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Widgets\\LocationPerformanceWidget.php' => 'App\\Filament\\Pos\\Widgets\\LocationPerformanceWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Widgets\\OutletAssignmentOverviewWidget.php' => 'App\\Filament\\Pos\\Widgets\\OutletAssignmentOverviewWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Widgets\\PaymentMethodsWidget.php' => 'App\\Filament\\Pos\\Widgets\\PaymentMethodsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Widgets\\RecentTransactionsWidget.php' => 'App\\Filament\\Pos\\Widgets\\RecentTransactionsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Widgets\\RevenueAnalyticsWidget.php' => 'App\\Filament\\Pos\\Widgets\\RevenueAnalyticsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Widgets\\SalesOverviewWidget.php' => 'App\\Filament\\Pos\\Widgets\\SalesOverviewWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Widgets\\TopProductsWidget.php' => 'App\\Filament\\Pos\\Widgets\\TopProductsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pos\\Widgets\\WelcomeWidget.php' => 'App\\Filament\\Pos\\Widgets\\WelcomeWidget',
    0 => 'App\\Filament\\Pos\\Widgets\\WelcomeWidget',
    1 => 'App\\Filament\\Pos\\Widgets\\SalesOverviewWidget',
    2 => 'App\\Filament\\Pos\\Widgets\\RevenueAnalyticsWidget',
    3 => 'App\\Filament\\Pos\\Widgets\\HourlySalesWidget',
    4 => 'App\\Filament\\Pos\\Widgets\\PaymentMethodsWidget',
    5 => 'App\\Filament\\Pos\\Widgets\\TopProductsWidget',
    6 => 'App\\Filament\\Pos\\Widgets\\LocationPerformanceWidget',
    7 => 'App\\Filament\\Pos\\Widgets\\RecentTransactionsWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Pos/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Pos\\Widgets',
  ),
);