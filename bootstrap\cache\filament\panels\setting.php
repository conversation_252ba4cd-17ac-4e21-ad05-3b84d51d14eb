<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.setting.resources.event-manager-resource.pages.create-event-manager' => 'App\\Filament\\Setting\\Resources\\EventManagerResource\\Pages\\CreateEventManager',
    'app.filament.setting.resources.event-manager-resource.pages.edit-event-manager' => 'App\\Filament\\Setting\\Resources\\EventManagerResource\\Pages\\EditEventManager',
    'app.filament.setting.resources.event-manager-resource.pages.list-event-managers' => 'App\\Filament\\Setting\\Resources\\EventManagerResource\\Pages\\ListEventManagers',
    'app.filament.setting.resources.notification-setting-resource.pages.create-notification-setting' => 'App\\Filament\\Setting\\Resources\\NotificationSettingResource\\Pages\\CreateNotificationSetting',
    'app.filament.setting.resources.notification-setting-resource.pages.edit-notification-setting' => 'App\\Filament\\Setting\\Resources\\NotificationSettingResource\\Pages\\EditNotificationSetting',
    'app.filament.setting.resources.notification-setting-resource.pages.list-notification-settings' => 'App\\Filament\\Setting\\Resources\\NotificationSettingResource\\Pages\\ListNotificationSettings',
    'app.filament.setting.resources.notification-setting-resource.pages.view-notification-setting' => 'App\\Filament\\Setting\\Resources\\NotificationSettingResource\\Pages\\ViewNotificationSetting',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Setting/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Setting\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Setting\\Resources\\EventManagerResource.php' => 'App\\Filament\\Setting\\Resources\\EventManagerResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Setting\\Resources\\NotificationSettingResource.php' => 'App\\Filament\\Setting\\Resources\\NotificationSettingResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Setting/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Setting\\Resources',
  ),
  'widgets' => 
  array (
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Setting/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Setting\\Widgets',
  ),
);