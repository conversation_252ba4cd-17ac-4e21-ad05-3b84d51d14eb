<?php

namespace App\Filament\Karyawan\Resources\AbsensiResource\Pages;

use App\Filament\Karyawan\Resources\AbsensiResource;
use App\Models\Absensi;
use App\Models\Karyawan;
use App\Models\Schedule;
use App\Services\PhotoMetadataService;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class CreateAbsensi extends CreateRecord
{
    protected static string $resource = AbsensiResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        try {
            // Debug logging - input data
            Log::info('mutateFormDataBeforeCreate called with data:', [
                'data_keys' => array_keys($data),
                'foto_absensi' => $data['foto_absensi'] ?? 'NOT SET',
                'full_data' => $data
            ]);

            $user = Auth::user();
            $karyawan = Karyawan::select(['id', 'nama_lengkap', 'id_entitas', 'id_user', 'status_aktif'])
                ->with('entitas')
                ->where('id_user', $user->id)
                ->first();

            // Validate employee is active FIRST
            if (!$karyawan || !$karyawan->status_aktif) {
                Notification::make()
                    ->title('Akses Ditolak! 🚫')
                    ->body('Hanya karyawan dengan status aktif yang dapat melakukan absensi. Silakan hubungi HRD untuk informasi lebih lanjut.')
                    ->danger()
                    ->persistent()
                    ->actions([
                        \Filament\Notifications\Actions\Action::make('ok')
                            ->label('Mengerti')
                            ->close(),
                    ])
                    ->send();

                $this->halt();
                return [];
            }

            // Get today's schedule first to check for entitas
            $today = Carbon::today()->format('Y-m-d');
            $jadwal = Schedule::where('karyawan_id', $karyawan->id)
                ->whereDate('tanggal_jadwal', $today)
                ->with(['shift', 'entitas'])
                ->first();

            // Validate location radius SECOND
            $lat = $data['latitude'] ?? null;
            $lng = $data['longitude'] ?? null;

            if ($lat && $lng && $karyawan) {
                // Prioritas 1: Gunakan entitas dari jadwal kerja jika ada
                $entity = null;
                if ($jadwal && $jadwal->entitas_id) {
                    $entity = $jadwal->entitas ?? \App\Models\Entitas::find($jadwal->entitas_id);
                }

                // Prioritas 2: Jika tidak ada entitas di jadwal, gunakan entitas karyawan
                if (!$entity && $karyawan->entitas) {
                    $entity = $karyawan->entitas;
                }

                if ($entity && $entity->latitude && $entity->longitude) {
                    $distance = $this->calculateDistance(
                        (float)$lat,
                        (float)$lng,
                        (float)$entity->latitude,
                        (float)$entity->longitude
                    );

                    $allowedRadius = $entity->radius ?? 100; // Default 100m

                    if ($distance > $allowedRadius) {
                        Notification::make()
                            ->title('Lokasi Anda Terlalu Jauh! 🚫')
                            ->body("Anda berada " . round($distance) . "m dari kantor ({$entity->nama}). Maksimal jarak yang diizinkan adalah {$allowedRadius}m. Silakan mendekati lokasi kantor untuk melakukan absensi.")
                            ->danger()
                            ->persistent()
                            ->actions([
                                \Filament\Notifications\Actions\Action::make('ok')
                                    ->label('Mengerti')
                                    ->close(),
                            ])
                            ->send();

                        // Halt the process but don't throw exception
                        $this->halt();
                        return [];
                    }
                }
            }

            // Jadwal sudah diambil di atas, tidak perlu mengambil lagi
            // Pastikan relasi shift ter-load
            if ($jadwal && !$jadwal->relationLoaded('shift')) {
                $jadwal->load('shift');
            }

            if (!$jadwal || !$jadwal->shift) {
                Notification::make()
                    ->title('Tidak Ada Jadwal Kerja 📅')
                    ->body('Tidak ada jadwal kerja untuk hari ini. Silakan hubungi HRD atau supervisor Anda untuk mengatur jadwal kerja.')
                    ->warning()
                    ->persistent()
                    ->actions([
                        \Filament\Notifications\Actions\Action::make('ok')
                            ->label('Mengerti')
                            ->close(),
                    ])
                    ->send();

                // Halt the process gracefully
                $this->halt();
                return [];
            }

            // Get current attendance status for split shift
            $attendanceStatus = Absensi::getCurrentAttendanceStatus($karyawan->id, $today, $jadwal->shift);

            if ($attendanceStatus['action'] === 'completed') {
                Notification::make()
                    ->title('Absensi Sudah Lengkap ✅')
                    ->body($attendanceStatus['message'])
                    ->success()
                    ->persistent()
                    ->actions([
                        \Filament\Notifications\Actions\Action::make('ok')
                            ->label('Mengerti')
                            ->close(),
                    ])
                    ->send();

                $this->halt();
                return [];
            }

            if ($attendanceStatus['action'] === 'waiting') {
                Notification::make()
                    ->title('Menunggu Waktu Absensi ⏰')
                    ->body($attendanceStatus['message'])
                    ->warning()
                    ->persistent()
                    ->actions([
                        \Filament\Notifications\Actions\Action::make('ok')
                            ->label('Mengerti')
                            ->close(),
                    ])
                    ->send();

                $this->halt();
                return [];
            }

            $isCheckIn = $attendanceStatus['action'] === 'check_in';
            $currentPeriode = $attendanceStatus['periode'];

            // Get existing attendance record for current periode if it's check-out
            $existingAbsensi = null;
            if (!$isCheckIn) {
                // First try to find attendance for today
                $existingAbsensi = Absensi::with(['jadwal.shift'])
                    ->where('karyawan_id', $karyawan->id)
                    ->whereDate('tanggal_absensi', $today)
                    ->where('periode', $currentPeriode)
                    ->first();

                // If not found and it's a beda hari shift, look for yesterday's attendance
                if (!$existingAbsensi) {
                    $existingAbsensi = Absensi::findCheckoutableAttendance($karyawan->id, $today);

                    // Verify the found attendance matches the current period
                    if ($existingAbsensi && $existingAbsensi->periode != $currentPeriode) {
                        $existingAbsensi = null;
                    }
                }
            }

            $now = Carbon::now('Asia/Jakarta');
            $latitude = $data['latitude'] ?? null;
            $longitude = $data['longitude'] ?? null;
            $locationText = $latitude && $longitude ? $latitude . ',' . $longitude : null;

            // Process photo metadata if available
            $photoMetadata = null;
            if (isset($data['photo_metadata'])) {
                $photoMetadata = json_decode($data['photo_metadata'], true);

                // Determine attendance status
                $status = PhotoMetadataService::determineAttendanceStatus($karyawan->id, $now);

                // Update metadata with status
                if ($photoMetadata) {
                    $photoMetadata['status_kehadiran'] = $status;
                    $photoMetadata['processed_at'] = $now->toISOString();
                }
            }

            if ($isCheckIn) {
                // Tentukan entitas_id berdasarkan prioritas
                $entitasId = null;
                if ($jadwal && $jadwal->entitas_id) {
                    // Prioritas 1: Gunakan entitas dari jadwal kerja
                    $entitasId = $jadwal->entitas_id;
                } elseif ($karyawan->entitas_id) {
                    // Prioritas 2: Gunakan entitas dari karyawan
                    $entitasId = $karyawan->entitas_id;
                }

                $data['karyawan_id'] = $karyawan->id;
                $data['tanggal_absensi'] = $today;
                $data['periode'] = $currentPeriode;
                $data['waktu_masuk'] = $now;
                $data['lokasi_masuk'] = $locationText;
                $data['latitude_masuk'] = $latitude;
                $data['longitude_masuk'] = $longitude;
                $data['foto_masuk'] = $data['foto_absensi'] ?? null;
                $data['metadata_foto_masuk'] = $photoMetadata;
                $data['status'] = \App\Services\AttendanceService::determineAttendanceStatus($karyawan->id, $now, $currentPeriode);
                $data['jadwal_id'] = $jadwal?->id;
                $data['entitas_id'] = $entitasId;

                // Handle keterangan field - make sure it's set even if empty
                if (!isset($data['keterangan'])) {
                    $data['keterangan'] = null;
                }

                // Debug logging
                Log::info('Check-in data prepared:', [
                    'periode' => $currentPeriode,
                    'foto_masuk' => $data['foto_masuk'],
                    'foto_absensi' => $data['foto_absensi'] ?? 'NOT SET',
                    'has_foto_masuk_in_data' => isset($data['foto_masuk']),
                    'all_keys' => array_keys($data)
                ]);
            } else {
                // This is a checkout, so we'll update the existing record instead
                if (!$existingAbsensi) {
                    Notification::make()
                        ->title('Error Check-Out ❌')
                        ->body('Record absensi tidak ditemukan untuk check-out. Silakan lakukan check-in terlebih dahulu.')
                        ->danger()
                        ->persistent()
                        ->actions([
                            \Filament\Notifications\Actions\Action::make('ok')
                                ->label('Mengerti')
                                ->close(),
                        ])
                        ->send();

                    $this->halt();
                    return [];
                }

                // Debug logging
                Log::info('Check-out data prepared:', [
                    'periode' => $currentPeriode,
                    'foto_absensi' => $data['foto_absensi'] ?? 'NOT SET',
                    'has_foto_absensi_in_data' => isset($data['foto_absensi']),
                    'all_keys' => array_keys($data)
                ]);

                $existingAbsensi->update([
                    'waktu_keluar' => $now,
                    'lokasi_keluar' => $locationText,
                    'latitude_keluar' => $latitude,
                    'longitude_keluar' => $longitude,
                    'foto_keluar' => $data['foto_absensi'] ?? null,
                    'metadata_foto_keluar' => $photoMetadata,
                    'keterangan' => isset($data['keterangan']) && !empty($data['keterangan'])
                        ? ($existingAbsensi->keterangan ? $existingAbsensi->keterangan . "\n" . $data['keterangan'] : $data['keterangan'])
                        : $existingAbsensi->keterangan,
                ]);

                $periodeName = $currentPeriode == 1 ? 'Periode 1' : 'Periode 2';
                Notification::make()
                    ->title("Absensi keluar {$periodeName} berhasil")
                    ->body("Absensi keluar {$periodeName} Anda telah berhasil dicatat.")
                    ->success()
                    ->send();

                // Return empty data to prevent creating a new record
                return [];
            }

            return $data;
        } catch (\Exception $e) {
            Log::error('Error in mutateFormDataBeforeCreate:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data ?? 'NO DATA'
            ]);

            Notification::make()
                ->title('Terjadi kesalahan')
                ->body('Terjadi kesalahan saat memproses data absensi. Silakan coba lagi.')
                ->danger()
                ->send();

            throw $e;
        }
    }

    protected function handleRecordCreation(array $data): Model
    {
        // If this is a checkout, we don't want to create a new record
        if (empty($data)) {
            // Return the existing record instead
            $user = Auth::user();
            $karyawan = Karyawan::select(['id', 'nama_lengkap', 'id_entitas', 'id_user'])
                ->where('id_user', $user->id)
                ->first();
            $today = Carbon::today()->format('Y-m-d');

            return Absensi::where('karyawan_id', $karyawan?->id)
                ->where('tanggal_absensi', $today)
                ->first();
        }

        return parent::handleRecordCreation($data);
    }



    protected function beforeCreate(): void
    {
        $user = Auth::user();
        $karyawan = Karyawan::select(['id', 'nama_lengkap', 'id_entitas', 'id_user'])
            ->where('id_user', $user->id)
            ->first();

        if (!$karyawan) {

            Notification::make()
                ->title('Akun tidak terhubung dengan data karyawan')
                ->body('Silahkan hubungi administrator untuk mengaitkan akun Anda dengan data karyawan.')
                ->danger()
                ->persistent()
                ->send();

            $this->redirect($this->getResource()::getUrl('index'));
            return;
        }

        // Get and validate form data
        try {
            $data = $this->form->getState();

            // Check location data
            $lat = $data['latitude'] ?? null;
            $lng = $data['longitude'] ?? null;

            if (empty($lat) || empty($lng) || $lat == 0 || $lng == 0) {

                Notification::make()
                    ->title('Lokasi tidak terdeteksi')
                    ->body('Mohon izinkan akses lokasi untuk melakukan absensi. Pastikan Anda mengizinkan akses lokasi di browser Anda.')
                    ->danger()
                    ->persistent()
                    ->actions([
                        \Filament\Notifications\Actions\Action::make('reload')
                            ->label('Refresh Halaman')
                            ->url(url()->current())
                            ->openUrlInNewTab(false),
                        \Filament\Notifications\Actions\Action::make('help')
                            ->label('Bantuan')
                            ->url('https://support.google.com/chrome/answer/142065?hl=id')
                            ->openUrlInNewTab(true),
                    ])
                    ->send();

                $this->halt();
                return;
            }
        } catch (\Exception $e) {
            Log::error('Error in beforeCreate:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            Notification::make()
                ->title('Terjadi kesalahan')
                ->body('Terjadi kesalahan saat memproses data. Silakan coba lagi.')
                ->danger()
                ->send();

            $this->halt();
        }
    }

    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371000; // Earth's radius in meters

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLng / 2) * sin($dLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c; // Distance in meters
    }
}
