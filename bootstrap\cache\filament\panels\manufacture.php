<?php return array (
  'livewireComponents' => 
  array (
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'filament.widgets.filament-info-widget' => 'Filament\\Widgets\\FilamentInfoWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Manufacture/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Manufacture\\Pages',
  ),
  'resources' => 
  array (
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Manufacture/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Manufacture\\Resources',
  ),
  'widgets' => 
  array (
    0 => 'Filament\\Widgets\\AccountWidget',
    1 => 'Filament\\Widgets\\FilamentInfoWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Manufacture/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Manufacture\\Widgets',
  ),
);