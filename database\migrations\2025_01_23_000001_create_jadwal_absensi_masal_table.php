<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jadwal_absensi_masal', function (Blueprint $table) {
            $table->id();
            $table->string('nama_jadwal');
            $table->date('tanggal_mulai');
            $table->date('tanggal_selesai');
            $table->foreignId('shift_id')->constrained('shift')->onDelete('cascade');
            $table->foreignId('entitas_id')->nullable()->constrained('entitas')->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->text('keterangan')->nullable();
            $table->timestamp('generated_at')->nullable()->comment('Waktu ketika jadwal dan absensi di-generate');
            $table->enum('status', ['draft', 'completed', 'cancelled'])->default('draft');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['tanggal_mulai', 'tanggal_selesai']);
            $table->index(['shift_id', 'entitas_id']);
            $table->index('created_by');
            $table->index('status');
        });

        // Pivot table for many-to-many relationship with karyawan
        Schema::create('jadwal_absensi_masal_karyawan', function (Blueprint $table) {
            $table->id();
            $table->foreignId('jadwal_absensi_masal_id')->constrained('jadwal_absensi_masal')->onDelete('cascade');
            $table->foreignId('karyawan_id')->constrained('karyawan')->onDelete('cascade');
            $table->timestamps();

            // Unique constraint to prevent duplicate assignments
            $table->unique(['jadwal_absensi_masal_id', 'karyawan_id'], 'unique_jadwal_absensi_karyawan');
            
            // Indexes
            $table->index('jadwal_absensi_masal_id', 'idx_jadwal_absensi_masal');
            $table->index('karyawan_id', 'idx_karyawan_jadwal_absensi');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jadwal_absensi_masal_karyawan');
        Schema::dropIfExists('jadwal_absensi_masal');
    }
};
