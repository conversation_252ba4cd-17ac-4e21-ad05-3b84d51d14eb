<?php

namespace App\Models;

use EightyNine\Approvals\Models\ApprovableModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lembur extends ApprovableModel
{
    use HasFactory, SoftDeletes, \App\Traits\HasOptimizedQueries;

    protected $table = 'lembur';

    protected $fillable = [
        'karyawan_id',
        
        'tanggal',
        'jumlah_jam',
        'jenis_lembur',
        'jenis_lembur_id',
        'upah_lembur',
        'deskripsi',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'tanggal'];

    // Relationship dengan Karyawan
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class, 'karyawan_id');
    }

    // Relationship dengan User (creator)
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Relationship dengan JenisLembur
    public function jenisLembur()
    {
        return $this->belongsTo(JenisLembur::class, 'jenis_lembur_id');
    }

    // Scope untuk filter berdasarkan bulan
    public function scopeFilterByMonth($query, $month, $year = null)
    {
        $year = $year ?? date('Y');
        return $query->whereMonth('tanggal', $month)
            ->whereYear('tanggal', $year);
    }

    /**
     * Hitung upah lembur berdasarkan jenis lembur yang dipilih
     */
    public function hitungUpahLembur($upahBulanan = null)
    {
        if (!$upahBulanan) {
            // Ambil upah bulanan dari basis gaji karyawan (periode_gaji = 'Basis Gaji')
            $basisGaji = $this->karyawan->penggajian()
                ->where('periode_gaji', 'Basis Gaji')
                ->latest()
                ->first();
            $upahBulanan = $basisGaji ? $basisGaji->gaji_pokok : 0;
        }

        if ($upahBulanan <= 0) {
            return 0;
        }

        // Jika ada jenis lembur yang dipilih, gunakan itu
        if ($this->jenis_lembur_id && $this->jenisLembur) {
            return $this->jenisLembur->hitungUpahLembur($this->jumlah_jam, $upahBulanan);
        }

        // Fallback ke sistem lama jika belum ada jenis lembur
        $jenisHari = $this->getJenisHari();
        return $this->hitungUpahLemburPerJam($upahBulanan, $jenisHari);
    }

    /**
     * Hitung upah lembur per jam berdasarkan aturan NON-BERTINGKAT (fallback)
     */
    private function hitungUpahLemburPerJam($upahBulanan, $jenisHari)
    {
        // Ambil aturan berdasarkan jenis hari
        $aturanList = $jenisHari === 'hari_libur'
            ? \App\Models\AturanLembur::getHolidayRules()
            : \App\Models\AturanLembur::getWeekdayRules();

        // SISTEM NON-BERTINGKAT: Cari aturan yang sesuai dengan jam lembur
        foreach ($aturanList as $aturan) {
            $jamMulai = $aturan->jam_mulai;
            $jamSelesai = $aturan->jam_selesai ?: 999;

            // Jika jam lembur masuk dalam range aturan ini
            if ($this->jumlah_jam >= $jamMulai && ($jamSelesai == 999 || $this->jumlah_jam <= $jamSelesai)) {
                // NON-BERTINGKAT: SEMUA jam mendapat multiplier yang sama
                return $aturan->hitungUpahLembur($this->jumlah_jam, $upahBulanan);
            }
        }

        return 0;
    }

    /**
     * Tentukan jenis hari berdasarkan tanggal lembur
     */
    private function getJenisHari()
    {
        $tanggal = \Carbon\Carbon::parse($this->tanggal);
        $dayOfWeek = $tanggal->dayOfWeek;

        // Cek apakah tanggal merah (hari libur nasional)
        // Untuk sementara, anggap Sabtu dan Minggu sebagai hari libur
        if ($dayOfWeek == 0 || $dayOfWeek == 6) { // Minggu = 0, Sabtu = 6
            return 'hari_libur';
        }

        // Untuk implementasi lebih lanjut, bisa ditambahkan pengecekan
        // terhadap tabel hari libur nasional

        return 'hari_biasa';
    }

    /**
     * Get formatted upah lembur
     */
    public function getFormattedUpahLemburAttribute()
    {
        $upah = $this->hitungUpahLembur();
        return 'Rp ' . number_format($upah, 0, ',', '.');
    }

    /**
     * Get breakdown upah lembur untuk debugging
     */
    public function getBreakdownUpahLembur($upahBulanan = null)
    {
        if (!$upahBulanan) {
            // Ambil upah bulanan dari basis gaji karyawan (periode_gaji = 'Basis Gaji')
            $basisGaji = $this->karyawan->penggajian()
                ->where('periode_gaji', 'Basis Gaji')
                ->latest()
                ->first();
            $upahBulanan = $basisGaji ? $basisGaji->gaji_pokok : 0;
        }

        // Jika ada jenis lembur yang dipilih, gunakan breakdown dari JenisLembur
        if ($this->jenis_lembur_id && $this->jenisLembur) {
            $breakdown = $this->jenisLembur->getBreakdownUpahLembur($this->jumlah_jam, $upahBulanan);
            $breakdown['total_upah'] = $this->jenisLembur->hitungUpahLembur($this->jumlah_jam, $upahBulanan);
            $breakdown['jenis_hari'] = $this->jenisLembur->nama_jenis; // Tambahkan jenis_hari
            return $breakdown;
        }

        // Fallback ke sistem lama
        $jenisHari = $this->getJenisHari();
        $breakdown = [
            'jenis_lembur' => 'Sistem Lama - ' . ucfirst(str_replace('_', ' ', $jenisHari)),
            'jenis_hari' => $jenisHari,
            'tipe_perhitungan' => 'per_jam',
            'jumlah_jam' => $this->jumlah_jam,
            'upah_bulanan' => $upahBulanan,
            'detail_perhitungan' => [],
            'total_upah' => 0,
        ];

        // Untuk lembur per jam (sistem lama)
        $jamSisa = $this->jumlah_jam;
        $aturanList = $jenisHari === 'hari_libur'
            ? \App\Models\AturanLembur::getHolidayRules()
            : \App\Models\AturanLembur::getWeekdayRules();

        foreach ($aturanList as $aturan) {
            if ($jamSisa <= 0) break;

            $jamMulai = $aturan->jam_mulai;
            $jamSelesai = $aturan->jam_selesai ?: 999;

            $jamBerlaku = 0;

            if ($jamSisa >= $jamMulai) {
                if ($jamSelesai == 999) {
                    $jamBerlaku = $jamSisa - $jamMulai + 1;
                } else {
                    $jamBerlaku = min($jamSisa, $jamSelesai - $jamMulai + 1);
                }

                if ($jamBerlaku > 0) {
                    $upahJam = $upahBulanan / (30 * 8); // Asumsi 30 hari, 8 jam per hari
                    $upahAturan = $upahJam * $jamBerlaku * $aturan->multiplier;
                    $breakdown['detail_perhitungan'][] = [
                        'aturan' => 'Sistem Lama - ' . $aturan->keterangan,
                        'jam_berlaku' => $jamBerlaku,
                        'multiplier' => $aturan->multiplier,
                        'upah' => $upahAturan,
                    ];
                    $breakdown['total_upah'] += $upahAturan;
                    $jamSisa -= $jamBerlaku;
                }
            }
        }

        return $breakdown;
    }

    // Scope untuk filter berdasarkan karyawan
    public function scopeFilterByKaryawan($query, $karyawanId)
    {
        return $query->where('karyawan_id', $karyawanId);
    }

    /**
     * Scope to eager load approval relationships to prevent lazy loading issues
     */
    public function scopeWithApprovalRelations($query)
    {
        return $query->with([
            'approvalStatus',
            'lastApproval',
            'approvals'
        ]);
    }

    /**
     * Override approve method to fix status update bug
     *
     * Package EightyNine\Approvals has a bug where approval status
     * is not updated properly after approval action is recorded.
     * This override ensures the status is updated correctly.
     */
    public function approve($comment = null, ?\Illuminate\Contracts\Auth\Authenticatable $user = null): \RingleSoft\LaravelProcessApproval\Models\ProcessApproval|\Illuminate\Http\RedirectResponse|bool
    {
        try {
            \Illuminate\Support\Facades\DB::beginTransaction();

            // Call parent approve method from ApprovableModel
            $result = parent::approve($comment, $user);

            // Fix: Manually update approval status if it's still not 'Approved'
            if ($this->approvalStatus && $this->approvalStatus->status !== 'Approved') {
                // Check if approval is actually completed
                if ($this->isApprovalCompleted()) {
                    $this->approvalStatus->update([
                        'status' => 'Approved',
                        'updated_at' => now()
                    ]);

                    // Refresh the model to get updated status
                    $this->refresh();

                    \Illuminate\Support\Facades\Log::info('Fixed approval status for Lembur', [
                        'lembur_id' => $this->id,
                        'status_updated' => 'Pending -> Approved',
                        'user_id' => $user ? $user->id : null
                    ]);
                }
            }

            \Illuminate\Support\Facades\DB::commit();

            return $result;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\DB::rollback();
            \Illuminate\Support\Facades\Log::error('Error in Lembur approve method', [
                'lembur_id' => $this->id,
                'error' => $e->getMessage(),
                'user_id' => $user ? $user->id : null
            ]);
            throw $e;
        }
    }

    /**
     * Override canBeApprovedBy to fix hasRole issue with built-in roles
     * The parent method uses $user->hasRole($nextStep->role) which expects Shield roles,
     * but our approval flow uses built-in role strings like 'manager', 'admin', etc.
     */
    public function canBeApprovedBy(\Illuminate\Contracts\Auth\Authenticatable|null $user): bool|null
    {
        if (!$user) return false;

        $nextStep = $this->nextApprovalStep();
        if (!$nextStep) return false;

        // Check if approvals are paused or not submitted
        if ($this->approvalsPaused || !$this->isSubmitted()) {
            return false;
        }

        // Use built-in role checking instead of Shield hasRole
        // Check both role and role_id properties for compatibility
        $stepRole = $nextStep->role ?? $nextStep->role_id ?? null;

        if (!$stepRole) return false;

        return $user->role === $stepRole;
    }
}
