<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.accounting.resources.akun-resource.pages.create-akun' => 'App\\Filament\\Accounting\\Resources\\AkunResource\\Pages\\CreateAkun',
    'app.filament.accounting.resources.akun-resource.pages.edit-akun' => 'App\\Filament\\Accounting\\Resources\\AkunResource\\Pages\\EditAkun',
    'app.filament.accounting.resources.akun-resource.pages.list-akuns' => 'App\\Filament\\Accounting\\Resources\\AkunResource\\Pages\\ListAkuns',
    'app.filament.accounting.resources.aset-resource.pages.create-aset' => 'App\\Filament\\Accounting\\Resources\\AsetResource\\Pages\\CreateAset',
    'app.filament.accounting.resources.aset-resource.pages.edit-aset' => 'App\\Filament\\Accounting\\Resources\\AsetResource\\Pages\\EditAset',
    'app.filament.accounting.resources.aset-resource.pages.list-asets' => 'App\\Filament\\Accounting\\Resources\\AsetResource\\Pages\\ListAsets',
    'app.filament.accounting.resources.aset-resource.pages.view-aset' => 'App\\Filament\\Accounting\\Resources\\AsetResource\\Pages\\ViewAset',
    'app.filament.accounting.resources.bank-reconciliation-resource.pages.create-bank-reconciliation' => 'App\\Filament\\Accounting\\Resources\\BankReconciliationResource\\Pages\\CreateBankReconciliation',
    'app.filament.accounting.resources.bank-reconciliation-resource.pages.edit-bank-reconciliation' => 'App\\Filament\\Accounting\\Resources\\BankReconciliationResource\\Pages\\EditBankReconciliation',
    'app.filament.accounting.resources.bank-reconciliation-resource.pages.list-bank-reconciliations' => 'App\\Filament\\Accounting\\Resources\\BankReconciliationResource\\Pages\\ListBankReconciliations',
    'app.filament.accounting.resources.bank-reconciliation-resource.pages.reconcile-bank-reconciliation' => 'App\\Filament\\Accounting\\Resources\\BankReconciliationResource\\Pages\\ReconcileBankReconciliation',
    'app.filament.accounting.resources.bank-reconciliation-resource.pages.view-bank-reconciliation' => 'App\\Filament\\Accounting\\Resources\\BankReconciliationResource\\Pages\\ViewBankReconciliation',
    'app.filament.accounting.resources.inventory-resource.pages.create-inventory' => 'App\\Filament\\Accounting\\Resources\\InventoryResource\\Pages\\CreateInventory',
    'app.filament.accounting.resources.inventory-resource.pages.edit-inventory' => 'App\\Filament\\Accounting\\Resources\\InventoryResource\\Pages\\EditInventory',
    'app.filament.accounting.resources.inventory-resource.pages.list-inventories' => 'App\\Filament\\Accounting\\Resources\\InventoryResource\\Pages\\ListInventories',
    'app.filament.accounting.resources.journal-resource.pages.create-journal' => 'App\\Filament\\Accounting\\Resources\\JournalResource\\Pages\\CreateJournal',
    'app.filament.accounting.resources.journal-resource.pages.edit-journal' => 'App\\Filament\\Accounting\\Resources\\JournalResource\\Pages\\EditJournal',
    'app.filament.accounting.resources.journal-resource.pages.list-journals' => 'App\\Filament\\Accounting\\Resources\\JournalResource\\Pages\\ListJournals',
    'app.filament.accounting.resources.journal-resource.pages.view-journal' => 'App\\Filament\\Accounting\\Resources\\JournalResource\\Pages\\ViewJournal',
    'app.filament.accounting.resources.payment-method-resource.pages.create-payment-method' => 'App\\Filament\\Accounting\\Resources\\PaymentMethodResource\\Pages\\CreatePaymentMethod',
    'app.filament.accounting.resources.payment-method-resource.pages.edit-payment-method' => 'App\\Filament\\Accounting\\Resources\\PaymentMethodResource\\Pages\\EditPaymentMethod',
    'app.filament.accounting.resources.payment-method-resource.pages.list-payment-methods' => 'App\\Filament\\Accounting\\Resources\\PaymentMethodResource\\Pages\\ListPaymentMethods',
    'app.filament.accounting.resources.payment-method-resource.pages.view-payment-method' => 'App\\Filament\\Accounting\\Resources\\PaymentMethodResource\\Pages\\ViewPaymentMethod',
    'app.filament.accounting.resources.posting-rule-resource.pages.create-posting-rule' => 'App\\Filament\\Accounting\\Resources\\PostingRuleResource\\Pages\\CreatePostingRule',
    'app.filament.accounting.resources.posting-rule-resource.pages.edit-posting-rule' => 'App\\Filament\\Accounting\\Resources\\PostingRuleResource\\Pages\\EditPostingRule',
    'app.filament.accounting.resources.posting-rule-resource.pages.list-posting-rules' => 'App\\Filament\\Accounting\\Resources\\PostingRuleResource\\Pages\\ListPostingRules',
    'app.filament.accounting.resources.transaction-category-resource.pages.create-transaction-category' => 'App\\Filament\\Accounting\\Resources\\TransactionCategoryResource\\Pages\\CreateTransactionCategory',
    'app.filament.accounting.resources.transaction-category-resource.pages.edit-transaction-category' => 'App\\Filament\\Accounting\\Resources\\TransactionCategoryResource\\Pages\\EditTransactionCategory',
    'app.filament.accounting.resources.transaction-category-resource.pages.list-transaction-categories' => 'App\\Filament\\Accounting\\Resources\\TransactionCategoryResource\\Pages\\ListTransactionCategories',
    'app.filament.accounting.resources.transaction-category-resource.pages.view-transaction-category' => 'App\\Filament\\Accounting\\Resources\\TransactionCategoryResource\\Pages\\ViewTransactionCategory',
    'app.filament.accounting.pages.balance-sheet' => 'App\\Filament\\Accounting\\Pages\\BalanceSheet',
    'app.filament.accounting.pages.general-ledger' => 'App\\Filament\\Accounting\\Pages\\GeneralLedger',
    'app.filament.accounting.pages.import-sales' => 'App\\Filament\\Accounting\\Pages\\ImportSales',
    'app.filament.accounting.pages.income-statement' => 'App\\Filament\\Accounting\\Pages\\IncomeStatement',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'filament.widgets.filament-info-widget' => 'Filament\\Widgets\\FilamentInfoWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Pages\\BalanceSheet.php' => 'App\\Filament\\Accounting\\Pages\\BalanceSheet',
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Pages\\GeneralLedger.php' => 'App\\Filament\\Accounting\\Pages\\GeneralLedger',
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Pages\\ImportSales.php' => 'App\\Filament\\Accounting\\Pages\\ImportSales',
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Pages\\IncomeStatement.php' => 'App\\Filament\\Accounting\\Pages\\IncomeStatement',
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Accounting/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Accounting\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Resources\\AkunResource.php' => 'App\\Filament\\Accounting\\Resources\\AkunResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Resources\\AsetResource.php' => 'App\\Filament\\Accounting\\Resources\\AsetResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Resources\\BankReconciliationResource.php' => 'App\\Filament\\Accounting\\Resources\\BankReconciliationResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Resources\\InventoryResource.php' => 'App\\Filament\\Accounting\\Resources\\InventoryResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Resources\\JournalResource.php' => 'App\\Filament\\Accounting\\Resources\\JournalResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Resources\\PaymentMethodResource.php' => 'App\\Filament\\Accounting\\Resources\\PaymentMethodResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Resources\\PostingRuleResource.php' => 'App\\Filament\\Accounting\\Resources\\PostingRuleResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Accounting\\Resources\\TransactionCategoryResource.php' => 'App\\Filament\\Accounting\\Resources\\TransactionCategoryResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Accounting/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Accounting\\Resources',
  ),
  'widgets' => 
  array (
    0 => 'Filament\\Widgets\\AccountWidget',
    1 => 'Filament\\Widgets\\FilamentInfoWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Accounting/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Accounting\\Widgets',
  ),
);